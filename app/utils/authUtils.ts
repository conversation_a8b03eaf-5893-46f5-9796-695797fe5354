import * as QueryParams from "expo-auth-session/build/QueryParams"
import { supabase } from "app/config/config.base"
import * as WebBrowser from "expo-web-browser"
import { makeRedirectUri } from "expo-auth-session"
import * as Linking from "expo-linking"
import Constants, { ExecutionEnvironment } from "expo-constants"

const getRedirectUrl = (): string => {
  let redirectURL
  if (Constants.executionEnvironment === ExecutionEnvironment.StoreClient) {
    // Expo Go: Use host laptop's details
    const hostUri = Constants.expoConfig?.hostUri || "localhost:19000"
    const ipAndPort = hostUri.split(":")[0] === "localhost" ? "*************:19000" : hostUri
    redirectURL = `exp://${ipAndPort}/auth/magic`
  } else {
    redirectURL = Linking.createURL("auth/magic")
  }
  return redirectURL
}

export const createSessionFromUrl = async (url: string) => {
    const { params, errorCode } = QueryParams.getQueryParams(url);

    if (errorCode) throw new Error(errorCode);
    const { access_token, refresh_token } = params;

    if (!access_token) return;

    const { data, error } = await supabase.auth.setSession({
        access_token,
        refresh_token,
    });
    if (error) throw error;
    return data.session;
};

export const performOAuth = async () => {
    const redirectTo = makeRedirectUri({
        path:"onesub://"
    });

    const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "github",
        options: {
            redirectTo,
            skipBrowserRedirect: true,
        },
    });
    if (error) throw error;

    const res = await WebBrowser.openAuthSessionAsync(
        data?.url ?? "",
        redirectTo
    );

    if (res.type === "success") {
        const { url } = res;
        await createSessionFromUrl(url);
    }
};

export const sendMagicLink = async (email: string) => {
    const redirectTo = getRedirectUrl()

    const { error } = await supabase.auth.signInWithOtp({
        email: email,
        options: {
            emailRedirectTo: redirectTo,
        },
    });

    if (error) throw error;
};
