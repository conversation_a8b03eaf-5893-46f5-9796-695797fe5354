import React, { FC, useState } from "react"
import { observer } from "mobx-react-lite"
import { ViewStyle, TextStyle, View, Alert, Pressable } from "react-native"
import { AppStackScreenProps } from "app/navigators"
import { Screen, Text, ListItem, Toggle, Card } from "app/components"
import { DeleteAccountModal } from "app/components/DeleteAccountModal"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import {
  faUser,
  faBell,
  faShield,
  faQuestionCircle,
  faSignOutAlt,
  faChevronRight,
  faGlobe,
  faDollarSign,
  faMoon,
  faEnvelope,
  faLock,
  faEye,
  faInfoCircle,
  faHeadset,
  faStarHalfAlt
} from "@fortawesome/free-solid-svg-icons"
import { colors, spacing } from "app/theme"
import { navigate } from "app/navigators"
import { useAuth } from "app/contexts/AuthContext"
import { useStores } from "app/models"
import { accountDeletionService } from "app/services/AccountDeletionService"
import { supabase } from "app/config/config.base"

interface UserSettingsScreenProps extends AppStackScreenProps<"UserSettings"> {}

export const UserSettingsScreen: FC<UserSettingsScreenProps> = observer(function UserSettingsScreen() {
  // State for toggle settings
  const [pushNotifications, setPushNotifications] = useState(true)
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [darkMode, setDarkMode] = useState(false)
  const [dataCollection, setDataCollection] = useState(true)

  const { userId } = useAuth()

  const handleLogout = () => {
    Alert.alert(
      "Sign Out",
      "Are you sure you want to sign out?",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Sign Out",
          style: "destructive",
          onPress: () => {
            // TODO: Implement logout functionality
            Alert.alert("Logout", "Logout functionality to be implemented")
          },
        },
      ]
    )
  }

  const handleNavigation = (screen: string) => {
    Alert.alert("Navigation", `Navigate to ${screen} - To be implemented`)
  }

  return (
    <Screen style={$root} preset="scroll" contentContainerStyle={$scrollContainer}>
      {/* Notifications Section */}
      <Card
        style={$sectionCard}
        heading="Notifications"
        headingStyle={$sectionHeading}
        ContentComponent={
          <View>
            <View style={$toggleRow}>
              <View style={$toggleInfo}>
                <FontAwesomeIcon icon={faBell} color={colors.textDim} size={20} style={$toggleIcon} />
                <View>
                  <Text style={$toggleLabel}>Push Notifications</Text>
                  <Text style={$toggleDescription}>Receive alerts about your subscriptions</Text>
                </View>
              </View>
              <Toggle
                value={pushNotifications}
                onValueChange={setPushNotifications}
                variant="switch"
              />
            </View>

            <View style={$toggleRow}>
              <View style={$toggleInfo}>
                <FontAwesomeIcon icon={faEnvelope} color={colors.textDim} size={20} style={$toggleIcon} />
                <View>
                  <Text style={$toggleLabel}>Email Notifications</Text>
                  <Text style={$toggleDescription}>Get email updates and summaries</Text>
                </View>
              </View>
              <Toggle
                value={emailNotifications}
                onValueChange={setEmailNotifications}
                variant="switch"
              />
            </View>
          </View>
        }
      />

      {/* Privacy & Security Section */}
      <Card
        style={$sectionCard}
        heading="Privacy & Security"
        headingStyle={$sectionHeading}
        ContentComponent={
          <View>
            <ListItem
              text="Privacy Policy"
              leftIcon={faEye}
              rightIcon={faChevronRight}
              onPress={() => handleNavigation("Privacy Policy")}
              topSeparator
              bottomSeparator
            />

            <View style={$toggleRow}>
              <View style={$toggleInfo}>
                <FontAwesomeIcon icon={faShield} color={colors.textDim} size={20} style={$toggleIcon} />
                <View>
                  <Text style={$toggleLabel}>Data Collection</Text>
                  <Text style={$toggleDescription}>Help improve the app with usage data</Text>
                </View>
              </View>
              <Toggle
                value={dataCollection}
                onValueChange={setDataCollection}
                variant="switch"
              />
            </View>
          </View>
        }
      />

      {/* App Preferences Section */}
      {/*<Card*/}
      {/*  style={$sectionCard}*/}
      {/*  heading="App Preferences"*/}
      {/*  headingStyle={$sectionHeading}*/}
      {/*  ContentComponent={*/}
      {/*    <View>*/}
      {/*      <View style={$toggleRow}>*/}
      {/*        <View style={$toggleInfo}>*/}
      {/*          <FontAwesomeIcon icon={faMoon} color={colors.textDim} size={20} style={$toggleIcon} />*/}
      {/*          <View>*/}
      {/*            <Text style={$toggleLabel}>Dark Mode</Text>*/}
      {/*            <Text style={$toggleDescription}>Switch to dark theme</Text>*/}
      {/*          </View>*/}
      {/*        </View>*/}
      {/*        <Toggle*/}
      {/*          value={darkMode}*/}
      {/*          onValueChange={setDarkMode}*/}
      {/*          variant="switch"*/}
      {/*        />*/}
      {/*      </View>*/}

      {/*    </View>*/}
      {/*  }*/}
      {/*/>*/}

      {/* Support & About Section */}
      <Card
        style={$sectionCard}
        heading="Support & About"
        headingStyle={$sectionHeading}
        ContentComponent={
          <View>
            <ListItem
              text="Contact Support"
              leftIcon={faHeadset}
              rightIcon={faChevronRight}
              onPress={() => handleNavigation("Contact Support")}
              bottomSeparator
            />

          </View>
        }
      />

      {/* Logout Section */}
      <Card
        style={[$sectionCard, $logoutCard]}
        ContentComponent={
          <Pressable style={$logoutButton} onPress={handleLogout}>
            <FontAwesomeIcon icon={faSignOutAlt} color={colors.error} size={20} />
            <Text style={$logoutText}>Sign Out</Text>
          </Pressable>
        }
      />

      {/* App Version */}
      <View style={$versionContainer}>
        <Text style={$versionText}>OneSub v1.0.0</Text>
        {userId && (
          <Text style={$userIdText}>User ID: {userId.slice(0, 8)}...</Text>
        )}
      </View>
    </Screen>
  )
})

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.background,
}

const $scrollContainer: ViewStyle = {
  padding: spacing.md,
  paddingBottom: 80, // Space for bottom tab bar
}

const $sectionCard: ViewStyle = {
  marginBottom: spacing.md,
  backgroundColor: colors.palette.neutral100,
}

const $sectionHeading: TextStyle = {
  marginBottom: spacing.sm,
  fontSize: 18,
  fontWeight: "600",
  color: colors.text,
}

const $toggleRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  paddingVertical: spacing.sm,
  paddingHorizontal: spacing.xs,
  borderBottomWidth: 1,
  borderBottomColor: colors.separator,
}

const $toggleInfo: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $toggleIcon: ViewStyle = {
  marginRight: spacing.md,
  width: 24,
}

const $toggleLabel: TextStyle = {
  fontSize: 16,
  fontWeight: "500",
  color: colors.text,
  marginBottom: 2,
}

const $toggleDescription: TextStyle = {
  fontSize: 14,
  color: colors.textDim,
}

const $logoutCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderColor: colors.palette.angry100,
  borderWidth: 1,
}

const $logoutButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  paddingVertical: spacing.md,
}

const $logoutText: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.error,
  marginLeft: spacing.sm,
}

const $versionContainer: ViewStyle = {
  alignItems: "center",
  paddingVertical: spacing.lg,
  marginTop: spacing.md,
}

const $versionText: TextStyle = {
  fontSize: 14,
  color: colors.textDim,
  marginBottom: spacing.xs,
}

const $userIdText: TextStyle = {
  fontSize: 12,
  color: colors.textDim,
  fontFamily: "monospace",
}
