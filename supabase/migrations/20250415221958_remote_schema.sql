create extension if not exists "pg_trgm" with schema "public" version '1.6';

create table "public"."household_descriptors" (
    "household_id" uuid not null,
    "descriptor_id" uuid not null,
    "created_at" timestamp with time zone default now()
);


alter table "public"."household_descriptors" enable row level security;

create table "public"."household_profiles" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."household_profiles" enable row level security;

create table "public"."logos" (
    "id" uuid not null default uuid_generate_v4(),
    "url" text not null,
    "width" integer,
    "height" integer,
    "merchant_group_id" uuid,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."merchant_categories" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null
);


create table "public"."merchant_group_categories" (
    "merchant_group_id" uuid not null,
    "category_id" uuid not null
);


create table "public"."merchant_groups" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "description" text,
    "online_only" boolean not null default false,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."merchant_locations" (
    "id" uuid not null default uuid_generate_v4(),
    "merchant_id" uuid not null,
    "latitude" numeric(10,8) not null,
    "longitude" numeric(11,8) not null,
    "address" text,
    "place_id" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."merchants" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text,
    "merchant_group_id" uuid not null,
    "region_id" uuid,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."pending_questions" (
    "id" uuid not null default uuid_generate_v4(),
    "question_id" uuid not null,
    "target_type" text not null,
    "target_id" uuid not null,
    "position" integer not null default 0,
    "created_at" timestamp with time zone default now()
);


alter table "public"."pending_questions" enable row level security;

create table "public"."pricing_tier_ideal_for" (
    "pricing_tier_id" uuid not null,
    "tag" text not null
);


create table "public"."pricing_tiers" (
    "id" uuid not null default uuid_generate_v4(),
    "product_id" uuid not null,
    "name" text not null,
    "cost" numeric(10,2) not null,
    "currency" text not null,
    "description" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."product_ideal_for" (
    "product_id" uuid not null,
    "tag" text not null
);


create table "public"."products" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "description" text not null,
    "merchant_id" uuid not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."profile_descriptors" (
    "id" uuid not null default uuid_generate_v4(),
    "category" text not null,
    "code" text not null,
    "name" text not null,
    "icon" text,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."profile_question_options" (
    "id" uuid not null default uuid_generate_v4(),
    "question_id" uuid not null,
    "answer_text" text not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."profile_questions" (
    "id" uuid not null default uuid_generate_v4(),
    "question_text" text not null,
    "allow_multi_select" boolean default false,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."profiles" (
    "id" uuid not null,
    "updated_at" timestamp with time zone,
    "username" text,
    "full_name" text,
    "avatar_url" text,
    "website" text
);


alter table "public"."profiles" enable row level security;

create table "public"."question_option_descriptors" (
    "option_id" uuid not null,
    "descriptor_id" uuid not null
);


create table "public"."regions" (
    "id" uuid not null default uuid_generate_v4(),
    "name" text not null,
    "code" text not null,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


create table "public"."user_descriptors" (
    "user_id" uuid not null,
    "descriptor_id" uuid not null,
    "created_at" timestamp with time zone default now()
);


alter table "public"."user_descriptors" enable row level security;

create table "public"."user_profiles" (
    "id" uuid not null,
    "first_name" text not null,
    "last_name" text,
    "avatar_url" text,
    "household_id" uuid,
    "has_completed_onboarding" boolean default false,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."user_profiles" enable row level security;

create table "public"."user_subscriptions" (
    "id" uuid not null default uuid_generate_v4(),
    "user_id" uuid not null,
    "household_id" uuid,
    "profile_type" text not null,
    "product_id" uuid not null,
    "started_on" timestamp with time zone not null default now(),
    "ended_on" timestamp with time zone,
    "created_at" timestamp with time zone default now(),
    "updated_at" timestamp with time zone default now()
);


alter table "public"."user_subscriptions" enable row level security;

CREATE UNIQUE INDEX household_descriptors_pkey ON public.household_descriptors USING btree (household_id, descriptor_id);

CREATE UNIQUE INDEX household_profiles_pkey ON public.household_profiles USING btree (id);

CREATE INDEX idx_merchants_merchant_group_id ON public.merchants USING btree (merchant_group_id);

CREATE INDEX idx_pending_questions_target ON public.pending_questions USING btree (target_type, target_id);

CREATE INDEX idx_pricing_tiers_product_id ON public.pricing_tiers USING btree (product_id);

CREATE INDEX idx_products_merchant_id ON public.products USING btree (merchant_id);

CREATE INDEX idx_user_profiles_household_id ON public.user_profiles USING btree (household_id);

CREATE INDEX idx_user_subscriptions_household_id ON public.user_subscriptions USING btree (household_id);

CREATE INDEX idx_user_subscriptions_product_id ON public.user_subscriptions USING btree (product_id);

CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions USING btree (user_id);

CREATE UNIQUE INDEX logos_pkey ON public.logos USING btree (id);

CREATE UNIQUE INDEX merchant_categories_name_key ON public.merchant_categories USING btree (name);

CREATE UNIQUE INDEX merchant_categories_pkey ON public.merchant_categories USING btree (id);

CREATE UNIQUE INDEX merchant_group_categories_pkey ON public.merchant_group_categories USING btree (merchant_group_id, category_id);

CREATE UNIQUE INDEX merchant_groups_pkey ON public.merchant_groups USING btree (id);

CREATE UNIQUE INDEX merchant_locations_pkey ON public.merchant_locations USING btree (id);

CREATE UNIQUE INDEX merchants_pkey ON public.merchants USING btree (id);

CREATE UNIQUE INDEX pending_questions_pkey ON public.pending_questions USING btree (id);

CREATE UNIQUE INDEX pricing_tier_ideal_for_pkey ON public.pricing_tier_ideal_for USING btree (pricing_tier_id, tag);

CREATE UNIQUE INDEX pricing_tiers_pkey ON public.pricing_tiers USING btree (id);

CREATE UNIQUE INDEX product_ideal_for_pkey ON public.product_ideal_for USING btree (product_id, tag);

CREATE UNIQUE INDEX products_pkey ON public.products USING btree (id);

CREATE UNIQUE INDEX profile_descriptors_code_key ON public.profile_descriptors USING btree (code);

CREATE UNIQUE INDEX profile_descriptors_pkey ON public.profile_descriptors USING btree (id);

CREATE UNIQUE INDEX profile_question_options_pkey ON public.profile_question_options USING btree (id);

CREATE UNIQUE INDEX profile_questions_pkey ON public.profile_questions USING btree (id);

CREATE UNIQUE INDEX profiles_pkey ON public.profiles USING btree (id);

CREATE UNIQUE INDEX profiles_username_key ON public.profiles USING btree (username);

CREATE UNIQUE INDEX question_option_descriptors_pkey ON public.question_option_descriptors USING btree (option_id, descriptor_id);

CREATE UNIQUE INDEX regions_code_key ON public.regions USING btree (code);

CREATE UNIQUE INDEX regions_pkey ON public.regions USING btree (id);

CREATE UNIQUE INDEX user_descriptors_pkey ON public.user_descriptors USING btree (user_id, descriptor_id);

CREATE UNIQUE INDEX user_profiles_pkey ON public.user_profiles USING btree (id);

CREATE UNIQUE INDEX user_subscriptions_pkey ON public.user_subscriptions USING btree (id);

alter table "public"."household_descriptors" add constraint "household_descriptors_pkey" PRIMARY KEY using index "household_descriptors_pkey";

alter table "public"."household_profiles" add constraint "household_profiles_pkey" PRIMARY KEY using index "household_profiles_pkey";

alter table "public"."logos" add constraint "logos_pkey" PRIMARY KEY using index "logos_pkey";

alter table "public"."merchant_categories" add constraint "merchant_categories_pkey" PRIMARY KEY using index "merchant_categories_pkey";

alter table "public"."merchant_group_categories" add constraint "merchant_group_categories_pkey" PRIMARY KEY using index "merchant_group_categories_pkey";

alter table "public"."merchant_groups" add constraint "merchant_groups_pkey" PRIMARY KEY using index "merchant_groups_pkey";

alter table "public"."merchant_locations" add constraint "merchant_locations_pkey" PRIMARY KEY using index "merchant_locations_pkey";

alter table "public"."merchants" add constraint "merchants_pkey" PRIMARY KEY using index "merchants_pkey";

alter table "public"."pending_questions" add constraint "pending_questions_pkey" PRIMARY KEY using index "pending_questions_pkey";

alter table "public"."pricing_tier_ideal_for" add constraint "pricing_tier_ideal_for_pkey" PRIMARY KEY using index "pricing_tier_ideal_for_pkey";

alter table "public"."pricing_tiers" add constraint "pricing_tiers_pkey" PRIMARY KEY using index "pricing_tiers_pkey";

alter table "public"."product_ideal_for" add constraint "product_ideal_for_pkey" PRIMARY KEY using index "product_ideal_for_pkey";

alter table "public"."products" add constraint "products_pkey" PRIMARY KEY using index "products_pkey";

alter table "public"."profile_descriptors" add constraint "profile_descriptors_pkey" PRIMARY KEY using index "profile_descriptors_pkey";

alter table "public"."profile_question_options" add constraint "profile_question_options_pkey" PRIMARY KEY using index "profile_question_options_pkey";

alter table "public"."profile_questions" add constraint "profile_questions_pkey" PRIMARY KEY using index "profile_questions_pkey";

alter table "public"."profiles" add constraint "profiles_pkey" PRIMARY KEY using index "profiles_pkey";

alter table "public"."question_option_descriptors" add constraint "question_option_descriptors_pkey" PRIMARY KEY using index "question_option_descriptors_pkey";

alter table "public"."regions" add constraint "regions_pkey" PRIMARY KEY using index "regions_pkey";

alter table "public"."user_descriptors" add constraint "user_descriptors_pkey" PRIMARY KEY using index "user_descriptors_pkey";

alter table "public"."user_profiles" add constraint "user_profiles_pkey" PRIMARY KEY using index "user_profiles_pkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_pkey" PRIMARY KEY using index "user_subscriptions_pkey";

alter table "public"."household_descriptors" add constraint "household_descriptors_descriptor_id_fkey" FOREIGN KEY (descriptor_id) REFERENCES profile_descriptors(id) ON DELETE CASCADE not valid;

alter table "public"."household_descriptors" validate constraint "household_descriptors_descriptor_id_fkey";

alter table "public"."household_descriptors" add constraint "household_descriptors_household_id_fkey" FOREIGN KEY (household_id) REFERENCES household_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."household_descriptors" validate constraint "household_descriptors_household_id_fkey";

alter table "public"."logos" add constraint "logos_merchant_group_id_fkey" FOREIGN KEY (merchant_group_id) REFERENCES merchant_groups(id) ON DELETE CASCADE not valid;

alter table "public"."logos" validate constraint "logos_merchant_group_id_fkey";

alter table "public"."merchant_categories" add constraint "merchant_categories_name_key" UNIQUE using index "merchant_categories_name_key";

alter table "public"."merchant_group_categories" add constraint "merchant_group_categories_category_id_fkey" FOREIGN KEY (category_id) REFERENCES merchant_categories(id) ON DELETE CASCADE not valid;

alter table "public"."merchant_group_categories" validate constraint "merchant_group_categories_category_id_fkey";

alter table "public"."merchant_group_categories" add constraint "merchant_group_categories_merchant_group_id_fkey" FOREIGN KEY (merchant_group_id) REFERENCES merchant_groups(id) ON DELETE CASCADE not valid;

alter table "public"."merchant_group_categories" validate constraint "merchant_group_categories_merchant_group_id_fkey";

alter table "public"."merchant_locations" add constraint "merchant_locations_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE not valid;

alter table "public"."merchant_locations" validate constraint "merchant_locations_merchant_id_fkey";

alter table "public"."merchants" add constraint "merchants_merchant_group_id_fkey" FOREIGN KEY (merchant_group_id) REFERENCES merchant_groups(id) ON DELETE CASCADE not valid;

alter table "public"."merchants" validate constraint "merchants_merchant_group_id_fkey";

alter table "public"."merchants" add constraint "merchants_region_id_fkey" FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL not valid;

alter table "public"."merchants" validate constraint "merchants_region_id_fkey";

alter table "public"."pending_questions" add constraint "pending_questions_question_id_fkey" FOREIGN KEY (question_id) REFERENCES profile_questions(id) ON DELETE CASCADE not valid;

alter table "public"."pending_questions" validate constraint "pending_questions_question_id_fkey";

alter table "public"."pending_questions" add constraint "pending_questions_target_type_check" CHECK ((target_type = ANY (ARRAY['user'::text, 'household'::text]))) not valid;

alter table "public"."pending_questions" validate constraint "pending_questions_target_type_check";

alter table "public"."pricing_tier_ideal_for" add constraint "pricing_tier_ideal_for_pricing_tier_id_fkey" FOREIGN KEY (pricing_tier_id) REFERENCES pricing_tiers(id) ON DELETE CASCADE not valid;

alter table "public"."pricing_tier_ideal_for" validate constraint "pricing_tier_ideal_for_pricing_tier_id_fkey";

alter table "public"."pricing_tiers" add constraint "pricing_tiers_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."pricing_tiers" validate constraint "pricing_tiers_product_id_fkey";

alter table "public"."product_ideal_for" add constraint "product_ideal_for_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE not valid;

alter table "public"."product_ideal_for" validate constraint "product_ideal_for_product_id_fkey";

alter table "public"."products" add constraint "products_merchant_id_fkey" FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE not valid;

alter table "public"."products" validate constraint "products_merchant_id_fkey";

alter table "public"."profile_descriptors" add constraint "profile_descriptors_code_key" UNIQUE using index "profile_descriptors_code_key";

alter table "public"."profile_question_options" add constraint "profile_question_options_question_id_fkey" FOREIGN KEY (question_id) REFERENCES profile_questions(id) ON DELETE CASCADE not valid;

alter table "public"."profile_question_options" validate constraint "profile_question_options_question_id_fkey";

alter table "public"."profiles" add constraint "profiles_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."profiles" validate constraint "profiles_id_fkey";

alter table "public"."profiles" add constraint "profiles_username_key" UNIQUE using index "profiles_username_key";

alter table "public"."profiles" add constraint "username_length" CHECK ((char_length(username) >= 3)) not valid;

alter table "public"."profiles" validate constraint "username_length";

alter table "public"."question_option_descriptors" add constraint "question_option_descriptors_descriptor_id_fkey" FOREIGN KEY (descriptor_id) REFERENCES profile_descriptors(id) ON DELETE CASCADE not valid;

alter table "public"."question_option_descriptors" validate constraint "question_option_descriptors_descriptor_id_fkey";

alter table "public"."question_option_descriptors" add constraint "question_option_descriptors_option_id_fkey" FOREIGN KEY (option_id) REFERENCES profile_question_options(id) ON DELETE CASCADE not valid;

alter table "public"."question_option_descriptors" validate constraint "question_option_descriptors_option_id_fkey";

alter table "public"."regions" add constraint "regions_code_key" UNIQUE using index "regions_code_key";

alter table "public"."user_descriptors" add constraint "user_descriptors_descriptor_id_fkey" FOREIGN KEY (descriptor_id) REFERENCES profile_descriptors(id) ON DELETE CASCADE not valid;

alter table "public"."user_descriptors" validate constraint "user_descriptors_descriptor_id_fkey";

alter table "public"."user_descriptors" add constraint "user_descriptors_user_id_fkey" FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."user_descriptors" validate constraint "user_descriptors_user_id_fkey";

alter table "public"."user_profiles" add constraint "user_profiles_household_id_fkey" FOREIGN KEY (household_id) REFERENCES household_profiles(id) ON DELETE SET NULL not valid;

alter table "public"."user_profiles" validate constraint "user_profiles_household_id_fkey";

alter table "public"."user_profiles" add constraint "user_profiles_id_fkey" FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE not valid;

alter table "public"."user_profiles" validate constraint "user_profiles_id_fkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_household_id_fkey" FOREIGN KEY (household_id) REFERENCES household_profiles(id) ON DELETE SET NULL not valid;

alter table "public"."user_subscriptions" validate constraint "user_subscriptions_household_id_fkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT not valid;

alter table "public"."user_subscriptions" validate constraint "user_subscriptions_product_id_fkey";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_profile_type_check" CHECK ((profile_type = ANY (ARRAY['user'::text, 'household'::text]))) not valid;

alter table "public"."user_subscriptions" validate constraint "user_subscriptions_profile_type_check";

alter table "public"."user_subscriptions" add constraint "user_subscriptions_user_id_fkey" FOREIGN KEY (user_id) REFERENCES user_profiles(id) ON DELETE CASCADE not valid;

alter table "public"."user_subscriptions" validate constraint "user_subscriptions_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.fuzzy_search_products(search_term text)
 RETURNS TABLE(id uuid, name text, description text)
 LANGUAGE plpgsql
AS $function$BEGIN
    RETURN QUERY
    SELECT p.id, p.name, p.description
    FROM public.products p
    WHERE p.name ILIKE '%' || search_term || '%'
    OR p.description ILIKE '%' || search_term || '%'
    ORDER BY similarity(p.name, search_term) DESC
    LIMIT 10;
END;$function$
;

CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
begin
  insert into public.profiles (id, full_name, avatar_url)
  values (new.id, new.raw_user_meta_data->>'full_name', new.raw_user_meta_data->>'avatar_url');
  return new;
end;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$function$
;

grant delete on table "public"."household_descriptors" to "anon";

grant insert on table "public"."household_descriptors" to "anon";

grant references on table "public"."household_descriptors" to "anon";

grant select on table "public"."household_descriptors" to "anon";

grant trigger on table "public"."household_descriptors" to "anon";

grant truncate on table "public"."household_descriptors" to "anon";

grant update on table "public"."household_descriptors" to "anon";

grant delete on table "public"."household_descriptors" to "authenticated";

grant insert on table "public"."household_descriptors" to "authenticated";

grant references on table "public"."household_descriptors" to "authenticated";

grant select on table "public"."household_descriptors" to "authenticated";

grant trigger on table "public"."household_descriptors" to "authenticated";

grant truncate on table "public"."household_descriptors" to "authenticated";

grant update on table "public"."household_descriptors" to "authenticated";

grant delete on table "public"."household_descriptors" to "service_role";

grant insert on table "public"."household_descriptors" to "service_role";

grant references on table "public"."household_descriptors" to "service_role";

grant select on table "public"."household_descriptors" to "service_role";

grant trigger on table "public"."household_descriptors" to "service_role";

grant truncate on table "public"."household_descriptors" to "service_role";

grant update on table "public"."household_descriptors" to "service_role";

grant delete on table "public"."household_profiles" to "anon";

grant insert on table "public"."household_profiles" to "anon";

grant references on table "public"."household_profiles" to "anon";

grant select on table "public"."household_profiles" to "anon";

grant trigger on table "public"."household_profiles" to "anon";

grant truncate on table "public"."household_profiles" to "anon";

grant update on table "public"."household_profiles" to "anon";

grant delete on table "public"."household_profiles" to "authenticated";

grant insert on table "public"."household_profiles" to "authenticated";

grant references on table "public"."household_profiles" to "authenticated";

grant select on table "public"."household_profiles" to "authenticated";

grant trigger on table "public"."household_profiles" to "authenticated";

grant truncate on table "public"."household_profiles" to "authenticated";

grant update on table "public"."household_profiles" to "authenticated";

grant delete on table "public"."household_profiles" to "service_role";

grant insert on table "public"."household_profiles" to "service_role";

grant references on table "public"."household_profiles" to "service_role";

grant select on table "public"."household_profiles" to "service_role";

grant trigger on table "public"."household_profiles" to "service_role";

grant truncate on table "public"."household_profiles" to "service_role";

grant update on table "public"."household_profiles" to "service_role";

grant delete on table "public"."logos" to "anon";

grant insert on table "public"."logos" to "anon";

grant references on table "public"."logos" to "anon";

grant select on table "public"."logos" to "anon";

grant trigger on table "public"."logos" to "anon";

grant truncate on table "public"."logos" to "anon";

grant update on table "public"."logos" to "anon";

grant delete on table "public"."logos" to "authenticated";

grant insert on table "public"."logos" to "authenticated";

grant references on table "public"."logos" to "authenticated";

grant select on table "public"."logos" to "authenticated";

grant trigger on table "public"."logos" to "authenticated";

grant truncate on table "public"."logos" to "authenticated";

grant update on table "public"."logos" to "authenticated";

grant delete on table "public"."logos" to "service_role";

grant insert on table "public"."logos" to "service_role";

grant references on table "public"."logos" to "service_role";

grant select on table "public"."logos" to "service_role";

grant trigger on table "public"."logos" to "service_role";

grant truncate on table "public"."logos" to "service_role";

grant update on table "public"."logos" to "service_role";

grant delete on table "public"."merchant_categories" to "anon";

grant insert on table "public"."merchant_categories" to "anon";

grant references on table "public"."merchant_categories" to "anon";

grant select on table "public"."merchant_categories" to "anon";

grant trigger on table "public"."merchant_categories" to "anon";

grant truncate on table "public"."merchant_categories" to "anon";

grant update on table "public"."merchant_categories" to "anon";

grant delete on table "public"."merchant_categories" to "authenticated";

grant insert on table "public"."merchant_categories" to "authenticated";

grant references on table "public"."merchant_categories" to "authenticated";

grant select on table "public"."merchant_categories" to "authenticated";

grant trigger on table "public"."merchant_categories" to "authenticated";

grant truncate on table "public"."merchant_categories" to "authenticated";

grant update on table "public"."merchant_categories" to "authenticated";

grant delete on table "public"."merchant_categories" to "service_role";

grant insert on table "public"."merchant_categories" to "service_role";

grant references on table "public"."merchant_categories" to "service_role";

grant select on table "public"."merchant_categories" to "service_role";

grant trigger on table "public"."merchant_categories" to "service_role";

grant truncate on table "public"."merchant_categories" to "service_role";

grant update on table "public"."merchant_categories" to "service_role";

grant delete on table "public"."merchant_group_categories" to "anon";

grant insert on table "public"."merchant_group_categories" to "anon";

grant references on table "public"."merchant_group_categories" to "anon";

grant select on table "public"."merchant_group_categories" to "anon";

grant trigger on table "public"."merchant_group_categories" to "anon";

grant truncate on table "public"."merchant_group_categories" to "anon";

grant update on table "public"."merchant_group_categories" to "anon";

grant delete on table "public"."merchant_group_categories" to "authenticated";

grant insert on table "public"."merchant_group_categories" to "authenticated";

grant references on table "public"."merchant_group_categories" to "authenticated";

grant select on table "public"."merchant_group_categories" to "authenticated";

grant trigger on table "public"."merchant_group_categories" to "authenticated";

grant truncate on table "public"."merchant_group_categories" to "authenticated";

grant update on table "public"."merchant_group_categories" to "authenticated";

grant delete on table "public"."merchant_group_categories" to "service_role";

grant insert on table "public"."merchant_group_categories" to "service_role";

grant references on table "public"."merchant_group_categories" to "service_role";

grant select on table "public"."merchant_group_categories" to "service_role";

grant trigger on table "public"."merchant_group_categories" to "service_role";

grant truncate on table "public"."merchant_group_categories" to "service_role";

grant update on table "public"."merchant_group_categories" to "service_role";

grant delete on table "public"."merchant_groups" to "anon";

grant insert on table "public"."merchant_groups" to "anon";

grant references on table "public"."merchant_groups" to "anon";

grant select on table "public"."merchant_groups" to "anon";

grant trigger on table "public"."merchant_groups" to "anon";

grant truncate on table "public"."merchant_groups" to "anon";

grant update on table "public"."merchant_groups" to "anon";

grant delete on table "public"."merchant_groups" to "authenticated";

grant insert on table "public"."merchant_groups" to "authenticated";

grant references on table "public"."merchant_groups" to "authenticated";

grant select on table "public"."merchant_groups" to "authenticated";

grant trigger on table "public"."merchant_groups" to "authenticated";

grant truncate on table "public"."merchant_groups" to "authenticated";

grant update on table "public"."merchant_groups" to "authenticated";

grant delete on table "public"."merchant_groups" to "service_role";

grant insert on table "public"."merchant_groups" to "service_role";

grant references on table "public"."merchant_groups" to "service_role";

grant select on table "public"."merchant_groups" to "service_role";

grant trigger on table "public"."merchant_groups" to "service_role";

grant truncate on table "public"."merchant_groups" to "service_role";

grant update on table "public"."merchant_groups" to "service_role";

grant delete on table "public"."merchant_locations" to "anon";

grant insert on table "public"."merchant_locations" to "anon";

grant references on table "public"."merchant_locations" to "anon";

grant select on table "public"."merchant_locations" to "anon";

grant trigger on table "public"."merchant_locations" to "anon";

grant truncate on table "public"."merchant_locations" to "anon";

grant update on table "public"."merchant_locations" to "anon";

grant delete on table "public"."merchant_locations" to "authenticated";

grant insert on table "public"."merchant_locations" to "authenticated";

grant references on table "public"."merchant_locations" to "authenticated";

grant select on table "public"."merchant_locations" to "authenticated";

grant trigger on table "public"."merchant_locations" to "authenticated";

grant truncate on table "public"."merchant_locations" to "authenticated";

grant update on table "public"."merchant_locations" to "authenticated";

grant delete on table "public"."merchant_locations" to "service_role";

grant insert on table "public"."merchant_locations" to "service_role";

grant references on table "public"."merchant_locations" to "service_role";

grant select on table "public"."merchant_locations" to "service_role";

grant trigger on table "public"."merchant_locations" to "service_role";

grant truncate on table "public"."merchant_locations" to "service_role";

grant update on table "public"."merchant_locations" to "service_role";

grant delete on table "public"."merchants" to "anon";

grant insert on table "public"."merchants" to "anon";

grant references on table "public"."merchants" to "anon";

grant select on table "public"."merchants" to "anon";

grant trigger on table "public"."merchants" to "anon";

grant truncate on table "public"."merchants" to "anon";

grant update on table "public"."merchants" to "anon";

grant delete on table "public"."merchants" to "authenticated";

grant insert on table "public"."merchants" to "authenticated";

grant references on table "public"."merchants" to "authenticated";

grant select on table "public"."merchants" to "authenticated";

grant trigger on table "public"."merchants" to "authenticated";

grant truncate on table "public"."merchants" to "authenticated";

grant update on table "public"."merchants" to "authenticated";

grant delete on table "public"."merchants" to "service_role";

grant insert on table "public"."merchants" to "service_role";

grant references on table "public"."merchants" to "service_role";

grant select on table "public"."merchants" to "service_role";

grant trigger on table "public"."merchants" to "service_role";

grant truncate on table "public"."merchants" to "service_role";

grant update on table "public"."merchants" to "service_role";

grant delete on table "public"."pending_questions" to "anon";

grant insert on table "public"."pending_questions" to "anon";

grant references on table "public"."pending_questions" to "anon";

grant select on table "public"."pending_questions" to "anon";

grant trigger on table "public"."pending_questions" to "anon";

grant truncate on table "public"."pending_questions" to "anon";

grant update on table "public"."pending_questions" to "anon";

grant delete on table "public"."pending_questions" to "authenticated";

grant insert on table "public"."pending_questions" to "authenticated";

grant references on table "public"."pending_questions" to "authenticated";

grant select on table "public"."pending_questions" to "authenticated";

grant trigger on table "public"."pending_questions" to "authenticated";

grant truncate on table "public"."pending_questions" to "authenticated";

grant update on table "public"."pending_questions" to "authenticated";

grant delete on table "public"."pending_questions" to "service_role";

grant insert on table "public"."pending_questions" to "service_role";

grant references on table "public"."pending_questions" to "service_role";

grant select on table "public"."pending_questions" to "service_role";

grant trigger on table "public"."pending_questions" to "service_role";

grant truncate on table "public"."pending_questions" to "service_role";

grant update on table "public"."pending_questions" to "service_role";

grant delete on table "public"."pricing_tier_ideal_for" to "anon";

grant insert on table "public"."pricing_tier_ideal_for" to "anon";

grant references on table "public"."pricing_tier_ideal_for" to "anon";

grant select on table "public"."pricing_tier_ideal_for" to "anon";

grant trigger on table "public"."pricing_tier_ideal_for" to "anon";

grant truncate on table "public"."pricing_tier_ideal_for" to "anon";

grant update on table "public"."pricing_tier_ideal_for" to "anon";

grant delete on table "public"."pricing_tier_ideal_for" to "authenticated";

grant insert on table "public"."pricing_tier_ideal_for" to "authenticated";

grant references on table "public"."pricing_tier_ideal_for" to "authenticated";

grant select on table "public"."pricing_tier_ideal_for" to "authenticated";

grant trigger on table "public"."pricing_tier_ideal_for" to "authenticated";

grant truncate on table "public"."pricing_tier_ideal_for" to "authenticated";

grant update on table "public"."pricing_tier_ideal_for" to "authenticated";

grant delete on table "public"."pricing_tier_ideal_for" to "service_role";

grant insert on table "public"."pricing_tier_ideal_for" to "service_role";

grant references on table "public"."pricing_tier_ideal_for" to "service_role";

grant select on table "public"."pricing_tier_ideal_for" to "service_role";

grant trigger on table "public"."pricing_tier_ideal_for" to "service_role";

grant truncate on table "public"."pricing_tier_ideal_for" to "service_role";

grant update on table "public"."pricing_tier_ideal_for" to "service_role";

grant delete on table "public"."pricing_tiers" to "anon";

grant insert on table "public"."pricing_tiers" to "anon";

grant references on table "public"."pricing_tiers" to "anon";

grant select on table "public"."pricing_tiers" to "anon";

grant trigger on table "public"."pricing_tiers" to "anon";

grant truncate on table "public"."pricing_tiers" to "anon";

grant update on table "public"."pricing_tiers" to "anon";

grant delete on table "public"."pricing_tiers" to "authenticated";

grant insert on table "public"."pricing_tiers" to "authenticated";

grant references on table "public"."pricing_tiers" to "authenticated";

grant select on table "public"."pricing_tiers" to "authenticated";

grant trigger on table "public"."pricing_tiers" to "authenticated";

grant truncate on table "public"."pricing_tiers" to "authenticated";

grant update on table "public"."pricing_tiers" to "authenticated";

grant delete on table "public"."pricing_tiers" to "service_role";

grant insert on table "public"."pricing_tiers" to "service_role";

grant references on table "public"."pricing_tiers" to "service_role";

grant select on table "public"."pricing_tiers" to "service_role";

grant trigger on table "public"."pricing_tiers" to "service_role";

grant truncate on table "public"."pricing_tiers" to "service_role";

grant update on table "public"."pricing_tiers" to "service_role";

grant delete on table "public"."product_ideal_for" to "anon";

grant insert on table "public"."product_ideal_for" to "anon";

grant references on table "public"."product_ideal_for" to "anon";

grant select on table "public"."product_ideal_for" to "anon";

grant trigger on table "public"."product_ideal_for" to "anon";

grant truncate on table "public"."product_ideal_for" to "anon";

grant update on table "public"."product_ideal_for" to "anon";

grant delete on table "public"."product_ideal_for" to "authenticated";

grant insert on table "public"."product_ideal_for" to "authenticated";

grant references on table "public"."product_ideal_for" to "authenticated";

grant select on table "public"."product_ideal_for" to "authenticated";

grant trigger on table "public"."product_ideal_for" to "authenticated";

grant truncate on table "public"."product_ideal_for" to "authenticated";

grant update on table "public"."product_ideal_for" to "authenticated";

grant delete on table "public"."product_ideal_for" to "service_role";

grant insert on table "public"."product_ideal_for" to "service_role";

grant references on table "public"."product_ideal_for" to "service_role";

grant select on table "public"."product_ideal_for" to "service_role";

grant trigger on table "public"."product_ideal_for" to "service_role";

grant truncate on table "public"."product_ideal_for" to "service_role";

grant update on table "public"."product_ideal_for" to "service_role";

grant delete on table "public"."products" to "anon";

grant insert on table "public"."products" to "anon";

grant references on table "public"."products" to "anon";

grant select on table "public"."products" to "anon";

grant trigger on table "public"."products" to "anon";

grant truncate on table "public"."products" to "anon";

grant update on table "public"."products" to "anon";

grant delete on table "public"."products" to "authenticated";

grant insert on table "public"."products" to "authenticated";

grant references on table "public"."products" to "authenticated";

grant select on table "public"."products" to "authenticated";

grant trigger on table "public"."products" to "authenticated";

grant truncate on table "public"."products" to "authenticated";

grant update on table "public"."products" to "authenticated";

grant delete on table "public"."products" to "service_role";

grant insert on table "public"."products" to "service_role";

grant references on table "public"."products" to "service_role";

grant select on table "public"."products" to "service_role";

grant trigger on table "public"."products" to "service_role";

grant truncate on table "public"."products" to "service_role";

grant update on table "public"."products" to "service_role";

grant delete on table "public"."profile_descriptors" to "anon";

grant insert on table "public"."profile_descriptors" to "anon";

grant references on table "public"."profile_descriptors" to "anon";

grant select on table "public"."profile_descriptors" to "anon";

grant trigger on table "public"."profile_descriptors" to "anon";

grant truncate on table "public"."profile_descriptors" to "anon";

grant update on table "public"."profile_descriptors" to "anon";

grant delete on table "public"."profile_descriptors" to "authenticated";

grant insert on table "public"."profile_descriptors" to "authenticated";

grant references on table "public"."profile_descriptors" to "authenticated";

grant select on table "public"."profile_descriptors" to "authenticated";

grant trigger on table "public"."profile_descriptors" to "authenticated";

grant truncate on table "public"."profile_descriptors" to "authenticated";

grant update on table "public"."profile_descriptors" to "authenticated";

grant delete on table "public"."profile_descriptors" to "service_role";

grant insert on table "public"."profile_descriptors" to "service_role";

grant references on table "public"."profile_descriptors" to "service_role";

grant select on table "public"."profile_descriptors" to "service_role";

grant trigger on table "public"."profile_descriptors" to "service_role";

grant truncate on table "public"."profile_descriptors" to "service_role";

grant update on table "public"."profile_descriptors" to "service_role";

grant delete on table "public"."profile_question_options" to "anon";

grant insert on table "public"."profile_question_options" to "anon";

grant references on table "public"."profile_question_options" to "anon";

grant select on table "public"."profile_question_options" to "anon";

grant trigger on table "public"."profile_question_options" to "anon";

grant truncate on table "public"."profile_question_options" to "anon";

grant update on table "public"."profile_question_options" to "anon";

grant delete on table "public"."profile_question_options" to "authenticated";

grant insert on table "public"."profile_question_options" to "authenticated";

grant references on table "public"."profile_question_options" to "authenticated";

grant select on table "public"."profile_question_options" to "authenticated";

grant trigger on table "public"."profile_question_options" to "authenticated";

grant truncate on table "public"."profile_question_options" to "authenticated";

grant update on table "public"."profile_question_options" to "authenticated";

grant delete on table "public"."profile_question_options" to "service_role";

grant insert on table "public"."profile_question_options" to "service_role";

grant references on table "public"."profile_question_options" to "service_role";

grant select on table "public"."profile_question_options" to "service_role";

grant trigger on table "public"."profile_question_options" to "service_role";

grant truncate on table "public"."profile_question_options" to "service_role";

grant update on table "public"."profile_question_options" to "service_role";

grant delete on table "public"."profile_questions" to "anon";

grant insert on table "public"."profile_questions" to "anon";

grant references on table "public"."profile_questions" to "anon";

grant select on table "public"."profile_questions" to "anon";

grant trigger on table "public"."profile_questions" to "anon";

grant truncate on table "public"."profile_questions" to "anon";

grant update on table "public"."profile_questions" to "anon";

grant delete on table "public"."profile_questions" to "authenticated";

grant insert on table "public"."profile_questions" to "authenticated";

grant references on table "public"."profile_questions" to "authenticated";

grant select on table "public"."profile_questions" to "authenticated";

grant trigger on table "public"."profile_questions" to "authenticated";

grant truncate on table "public"."profile_questions" to "authenticated";

grant update on table "public"."profile_questions" to "authenticated";

grant delete on table "public"."profile_questions" to "service_role";

grant insert on table "public"."profile_questions" to "service_role";

grant references on table "public"."profile_questions" to "service_role";

grant select on table "public"."profile_questions" to "service_role";

grant trigger on table "public"."profile_questions" to "service_role";

grant truncate on table "public"."profile_questions" to "service_role";

grant update on table "public"."profile_questions" to "service_role";

grant delete on table "public"."profiles" to "anon";

grant insert on table "public"."profiles" to "anon";

grant references on table "public"."profiles" to "anon";

grant select on table "public"."profiles" to "anon";

grant trigger on table "public"."profiles" to "anon";

grant truncate on table "public"."profiles" to "anon";

grant update on table "public"."profiles" to "anon";

grant delete on table "public"."profiles" to "authenticated";

grant insert on table "public"."profiles" to "authenticated";

grant references on table "public"."profiles" to "authenticated";

grant select on table "public"."profiles" to "authenticated";

grant trigger on table "public"."profiles" to "authenticated";

grant truncate on table "public"."profiles" to "authenticated";

grant update on table "public"."profiles" to "authenticated";

grant delete on table "public"."profiles" to "service_role";

grant insert on table "public"."profiles" to "service_role";

grant references on table "public"."profiles" to "service_role";

grant select on table "public"."profiles" to "service_role";

grant trigger on table "public"."profiles" to "service_role";

grant truncate on table "public"."profiles" to "service_role";

grant update on table "public"."profiles" to "service_role";

grant delete on table "public"."question_option_descriptors" to "anon";

grant insert on table "public"."question_option_descriptors" to "anon";

grant references on table "public"."question_option_descriptors" to "anon";

grant select on table "public"."question_option_descriptors" to "anon";

grant trigger on table "public"."question_option_descriptors" to "anon";

grant truncate on table "public"."question_option_descriptors" to "anon";

grant update on table "public"."question_option_descriptors" to "anon";

grant delete on table "public"."question_option_descriptors" to "authenticated";

grant insert on table "public"."question_option_descriptors" to "authenticated";

grant references on table "public"."question_option_descriptors" to "authenticated";

grant select on table "public"."question_option_descriptors" to "authenticated";

grant trigger on table "public"."question_option_descriptors" to "authenticated";

grant truncate on table "public"."question_option_descriptors" to "authenticated";

grant update on table "public"."question_option_descriptors" to "authenticated";

grant delete on table "public"."question_option_descriptors" to "service_role";

grant insert on table "public"."question_option_descriptors" to "service_role";

grant references on table "public"."question_option_descriptors" to "service_role";

grant select on table "public"."question_option_descriptors" to "service_role";

grant trigger on table "public"."question_option_descriptors" to "service_role";

grant truncate on table "public"."question_option_descriptors" to "service_role";

grant update on table "public"."question_option_descriptors" to "service_role";

grant delete on table "public"."regions" to "anon";

grant insert on table "public"."regions" to "anon";

grant references on table "public"."regions" to "anon";

grant select on table "public"."regions" to "anon";

grant trigger on table "public"."regions" to "anon";

grant truncate on table "public"."regions" to "anon";

grant update on table "public"."regions" to "anon";

grant delete on table "public"."regions" to "authenticated";

grant insert on table "public"."regions" to "authenticated";

grant references on table "public"."regions" to "authenticated";

grant select on table "public"."regions" to "authenticated";

grant trigger on table "public"."regions" to "authenticated";

grant truncate on table "public"."regions" to "authenticated";

grant update on table "public"."regions" to "authenticated";

grant delete on table "public"."regions" to "service_role";

grant insert on table "public"."regions" to "service_role";

grant references on table "public"."regions" to "service_role";

grant select on table "public"."regions" to "service_role";

grant trigger on table "public"."regions" to "service_role";

grant truncate on table "public"."regions" to "service_role";

grant update on table "public"."regions" to "service_role";

grant delete on table "public"."user_descriptors" to "anon";

grant insert on table "public"."user_descriptors" to "anon";

grant references on table "public"."user_descriptors" to "anon";

grant select on table "public"."user_descriptors" to "anon";

grant trigger on table "public"."user_descriptors" to "anon";

grant truncate on table "public"."user_descriptors" to "anon";

grant update on table "public"."user_descriptors" to "anon";

grant delete on table "public"."user_descriptors" to "authenticated";

grant insert on table "public"."user_descriptors" to "authenticated";

grant references on table "public"."user_descriptors" to "authenticated";

grant select on table "public"."user_descriptors" to "authenticated";

grant trigger on table "public"."user_descriptors" to "authenticated";

grant truncate on table "public"."user_descriptors" to "authenticated";

grant update on table "public"."user_descriptors" to "authenticated";

grant delete on table "public"."user_descriptors" to "service_role";

grant insert on table "public"."user_descriptors" to "service_role";

grant references on table "public"."user_descriptors" to "service_role";

grant select on table "public"."user_descriptors" to "service_role";

grant trigger on table "public"."user_descriptors" to "service_role";

grant truncate on table "public"."user_descriptors" to "service_role";

grant update on table "public"."user_descriptors" to "service_role";

grant delete on table "public"."user_profiles" to "anon";

grant insert on table "public"."user_profiles" to "anon";

grant references on table "public"."user_profiles" to "anon";

grant select on table "public"."user_profiles" to "anon";

grant trigger on table "public"."user_profiles" to "anon";

grant truncate on table "public"."user_profiles" to "anon";

grant update on table "public"."user_profiles" to "anon";

grant delete on table "public"."user_profiles" to "authenticated";

grant insert on table "public"."user_profiles" to "authenticated";

grant references on table "public"."user_profiles" to "authenticated";

grant select on table "public"."user_profiles" to "authenticated";

grant trigger on table "public"."user_profiles" to "authenticated";

grant truncate on table "public"."user_profiles" to "authenticated";

grant update on table "public"."user_profiles" to "authenticated";

grant delete on table "public"."user_profiles" to "service_role";

grant insert on table "public"."user_profiles" to "service_role";

grant references on table "public"."user_profiles" to "service_role";

grant select on table "public"."user_profiles" to "service_role";

grant trigger on table "public"."user_profiles" to "service_role";

grant truncate on table "public"."user_profiles" to "service_role";

grant update on table "public"."user_profiles" to "service_role";

grant delete on table "public"."user_subscriptions" to "anon";

grant insert on table "public"."user_subscriptions" to "anon";

grant references on table "public"."user_subscriptions" to "anon";

grant select on table "public"."user_subscriptions" to "anon";

grant trigger on table "public"."user_subscriptions" to "anon";

grant truncate on table "public"."user_subscriptions" to "anon";

grant update on table "public"."user_subscriptions" to "anon";

grant delete on table "public"."user_subscriptions" to "authenticated";

grant insert on table "public"."user_subscriptions" to "authenticated";

grant references on table "public"."user_subscriptions" to "authenticated";

grant select on table "public"."user_subscriptions" to "authenticated";

grant trigger on table "public"."user_subscriptions" to "authenticated";

grant truncate on table "public"."user_subscriptions" to "authenticated";

grant update on table "public"."user_subscriptions" to "authenticated";

grant delete on table "public"."user_subscriptions" to "service_role";

grant insert on table "public"."user_subscriptions" to "service_role";

grant references on table "public"."user_subscriptions" to "service_role";

grant select on table "public"."user_subscriptions" to "service_role";

grant trigger on table "public"."user_subscriptions" to "service_role";

grant truncate on table "public"."user_subscriptions" to "service_role";

grant update on table "public"."user_subscriptions" to "service_role";

create policy "Users can update their household"
on "public"."household_profiles"
as permissive
for update
to public
using ((EXISTS ( SELECT 1
   FROM user_profiles
  WHERE ((user_profiles.household_id = household_profiles.id) AND (user_profiles.id = auth.uid())))));


create policy "Users can view their household"
on "public"."household_profiles"
as permissive
for select
to public
using ((EXISTS ( SELECT 1
   FROM user_profiles
  WHERE ((user_profiles.household_id = household_profiles.id) AND (user_profiles.id = auth.uid())))));


create policy "Public profiles are viewable by everyone."
on "public"."profiles"
as permissive
for select
to public
using (true);


create policy "Users can insert their own profile."
on "public"."profiles"
as permissive
for insert
to public
with check ((( SELECT auth.uid() AS uid) = id));


create policy "Users can update own profile."
on "public"."profiles"
as permissive
for update
to public
using ((( SELECT auth.uid() AS uid) = id));


create policy "Users can update their own profile"
on "public"."user_profiles"
as permissive
for update
to public
using ((auth.uid() = id));


create policy "Users can view their own profile"
on "public"."user_profiles"
as permissive
for select
to public
using ((auth.uid() = id));


create policy "Users can view their own subscriptions"
on "public"."user_subscriptions"
as permissive
for select
to public
using (((user_id = auth.uid()) OR ((profile_type = 'household'::text) AND (EXISTS ( SELECT 1
   FROM user_profiles
  WHERE ((user_profiles.household_id = user_subscriptions.household_id) AND (user_profiles.id = auth.uid())))))));


CREATE TRIGGER update_household_profiles_updated_at BEFORE UPDATE ON public.household_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_merchant_groups_updated_at BEFORE UPDATE ON public.merchant_groups FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON public.merchants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pricing_tiers_updated_at BEFORE UPDATE ON public.pricing_tiers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON public.products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON public.user_subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();


