import { supabase } from "app/config/config.base"
import { ErrorType, ErrorExperience, reportSentryError } from "app/utils/crashReporting"

export interface ServiceResponse<T> {
  success: boolean
  data?: T
  error?: string
  code?: string
}

export const ACCOUNT_DELETION_ERROR_CODES = {
  UNAUTHORIZED: "UNAUTHORIZED",
  SUPABASE_ERROR: "SUPABASE_ERROR",
  NETWORK_ERROR: "NETWORK_ERROR",
  UNKNOWN_ERROR: "UNKNOWN_ERROR",
} as const

export class AccountDeletionService {
  /**
   * Delete user account and all associated data
   */
  public async deleteUserAccount(userId: string): Promise<ServiceResponse<void>> {
    try {
      if (__DEV__ && console.tron) {
        console.tron.log('🗑️ Starting account deletion for user:', userId)
      }

      // First, verify the user is authenticated
      const { data: { user }, error: authError } = await supabase.auth.getUser()

      if (authError || !user || user.id !== userId) {
        const error = new Error('User not authenticated or unauthorized')
        reportSentryError(error, ErrorType.HANDLED, ErrorExperience.Notifications)

        if (__DEV__ && console.tron) {
          console.tron.error('❌ Authentication failed during account deletion:', authError)
        }

        return {
          success: false,
          error: 'Authentication failed',
          code: ACCOUNT_DELETION_ERROR_CODES.UNAUTHORIZED
        }
      }

      // Call the secure database function to delete all user data
      const { error: deletionError } = await supabase.rpc('delete_user_account_secure', {
        user_id_to_delete: userId
      })

      if (deletionError) {
        throw deletionError
      }

      if (__DEV__ && console.tron) {
        console.tron.log('✅ Account deletion completed successfully')
      }

      // Return success - the calling code will handle sign out and state clearing
      return { success: true }

    } catch (error) {
      const deletionError = error instanceof Error ? error : new Error('Failed to delete account')
      reportSentryError(deletionError, ErrorType.HANDLED, ErrorExperience.Notifications)

      if (__DEV__ && console.tron) {
        console.tron.error('❌ Account deletion failed:', error)
      }

      // Determine error code based on error type
      let errorCode = ACCOUNT_DELETION_ERROR_CODES.UNKNOWN_ERROR
      if (deletionError.message.includes('network') || deletionError.message.includes('fetch')) {
        errorCode = ACCOUNT_DELETION_ERROR_CODES.NETWORK_ERROR
      } else if (deletionError.message.includes('auth') || deletionError.message.includes('unauthorized')) {
        errorCode = ACCOUNT_DELETION_ERROR_CODES.UNAUTHORIZED
      } else {
        errorCode = ACCOUNT_DELETION_ERROR_CODES.SUPABASE_ERROR
      }

      return {
        success: false,
        error: deletionError.message,
        code: errorCode
      }
    }
  }

  /**
   * Get user-friendly error message based on error code
   */
  public getErrorMessage(errorCode?: string): string {
    switch (errorCode) {
      case ACCOUNT_DELETION_ERROR_CODES.UNAUTHORIZED:
        return "You are not authorized to perform this action. Please sign in again."
      case ACCOUNT_DELETION_ERROR_CODES.NETWORK_ERROR:
        return "Network error occurred. Please check your connection and try again."
      case ACCOUNT_DELETION_ERROR_CODES.SUPABASE_ERROR:
        return "A server error occurred. Please try again later."
      default:
        return "An unexpected error occurred. Please try again later."
    }
  }
}

export const accountDeletionService = new AccountDeletionService()
