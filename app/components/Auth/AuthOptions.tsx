import React, { FC, useState } from "react";
import {View, ViewStyle, TextStyle, Platform, ActivityIndicator, Alert} from "react-native";
import { Button } from "../../components/Button";
import { Text } from "../../components/Text";
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome";
import { getAvailableProviders, AuthProviderConfig } from "../../config/authProviders";
import { colors, spacing } from "../../theme";
import * as Linking from "expo-linking";
import {createSessionFromUrl, sendMagicLink} from "app/utils/authUtils";
import {openInbox} from "react-native-email-link";
import prompt from 'react-native-prompt-android';

interface AuthOptionsProps {
  onAuthenticated?: () => void;
  showEmailOption?: boolean;
  showSeparator?: boolean;
  style?: ViewStyle;
  displayMode?: "row" | "column";
}

export const AuthOptions: FC<AuthOptionsProps> = ({
  onAuthenticated,
  showEmailOption = true,
  showSeparator = true,
  style,
  displayMode = "column",
}) => {
  const [isLoading, setIsLoading] = useState<string | null>(null);
  const availableProviders = getAvailableProviders();

  const handleOAuth = async (provider: AuthProviderConfig) => {
    Alert.alert("OAuth", `${provider.name} login is not implemented yet`);
    try {
      setIsLoading(provider.id);
      const session = null
      if (session) {
        onAuthenticated?.();
      }
    } catch (error) {
      Alert.alert(`Error with ${provider.name} authentication: ${error}`);
    } finally {
      setIsLoading(null);

    }
  };

  // Look at AuthContext to understand how we handle deep links to create a session

  const gatherEmailAndSendLink = async (email: string) => {
      setIsLoading("email");
      try {
        await sendMagicLink(email);
        await openInbox({
          title:"Check your email",
          message:"Please check your email for a magic link to log back in to OneSub.",

        });
        onAuthenticated?.();
      } catch (error) {
        Alert.alert(`Error logging in: ${error}`);
      } finally {
        setIsLoading(null);
      }
    }

  const handleEmailAuth = () => {
    if (isLoading) return;
    prompt("Email Login", "Enter your email address", [
        {
          text: "Cancel",
          onPress: () => console.log("Cancel Pressed"),
          style: "cancel",
        },
        {
          text: "OK",
          onPress: gatherEmailAndSendLink
        }],
        {
          type: 'plain-text',
          cancelable: false, // Prevent dismissing by tapping outside
        }
    );
  };

  const isHighlighted = (provider: AuthProviderConfig): boolean => {
    if (provider.id === "google" && Platform.OS === "android") return true;
    if (provider.id === "apple" && Platform.OS === "ios") return true;
    return false;
  };

  if (displayMode === "row") {
    return (
      <View style={[$oauthButtonContainer, style]}>
        {availableProviders
          .filter(p => p.provider !== "email" || showEmailOption)
          .map((provider) => {
            const highlighted = isHighlighted(provider);
            const buttonStyle = provider.buttonStyle || {};

            return (
              <Button
                key={provider.id}
                preset="filled"
                style={[
                  $secondaryButtonStyle,
                  highlighted && $highlightedButton,
                ]}
                onPress={() => provider.id === "email" ? handleEmailAuth() : handleOAuth(provider)}
                disabled={!!isLoading}
              >
                {isLoading === provider.id ? (
                  <ActivityIndicator size="small" color={buttonStyle.iconColor || colors.text} />
                ) : (
                  <FontAwesomeIcon
                    icon={provider.icon}
                    size={24}
                    color={buttonStyle.iconColor || colors.text}
                  />
                )}
              </Button>
            );
          })}
      </View>
    );
  }

  return (
    <View style={[$container, style]}>
      {showSeparator && (
          <View style={$separatorContainer}>
            <View style={$separatorLine} />
            <Text preset="formHelper" style={$separatorText}>Already have an account?</Text>
            <View style={$separatorLine} />
          </View>
      )}
      {availableProviders
        .filter(p => p.provider !== "email" || (p.provider === "email" && showEmailOption))
        .map((provider, index) => {
          const highlighted = isHighlighted(provider);
          const buttonStyle = provider.buttonStyle || {};
          const isEmail = provider.id === "email";

          return (
            <Button
              key={provider.id}
              preset={provider.buttonPreset}
              text={provider.name}
              style={[
                $buttonStyle,
                // eslint-disable-next-line react-native/no-inline-styles
                {
                  backgroundColor: buttonStyle.backgroundColor,
                  borderColor: buttonStyle.borderColor,
                  marginBottom: index < availableProviders.length - 1 ? spacing.sm : 0,
                  borderWidth: buttonStyle.borderColor ? 1 : 0,
                },
                highlighted && $highlightedButton,
              ]}
              textStyle={[
                $buttonTextStyle,
                { color: buttonStyle.textColor },
                highlighted && $highlightedText,
              ]}
              onPress={() => isEmail ? handleEmailAuth() : handleOAuth(provider)}
              disabled={!!isLoading}
              LeftAccessory={() => (
                <View style={$iconContainer}>
                  {isLoading === provider.id ? (
                    <ActivityIndicator size="small" color={buttonStyle.iconColor || colors.text} />
                  ) : (
                    <FontAwesomeIcon
                      icon={provider.icon}
                      color={buttonStyle.iconColor || colors.text}
                      size={18}
                    />
                  )}
                </View>
              )}
            />
          );
        })}


    </View>
  );
}


const $container: ViewStyle = {
  padding: spacing.md,
}

const $buttonStyle: ViewStyle = {
  marginVertical: spacing.xxs,
  borderRadius: 8,
  minHeight: 24,
  paddingVertical: spacing.xs,
}

const $buttonTextStyle: TextStyle = {
  fontSize: 16,
  fontWeight: "500",
}

const $iconContainer: ViewStyle = {
  marginRight: spacing.sm,
  width: 20,
  alignItems: "center",
  justifyContent: "center",
}

const $separatorContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginVertical: spacing.md,
}

const $separatorLine: ViewStyle = {
  flex: 1,
  height: 1,
  backgroundColor: colors.separator,
}

const $separatorText: TextStyle = {
  marginHorizontal: spacing.sm,
  color: colors.textDim,
}

const $highlightedButton: ViewStyle = {
  borderWidth: 2,
  borderColor: colors.palette.primary500,
}

const $highlightedText: TextStyle = {
  fontWeight: "600",
}

const $oauthButtonContainer: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-evenly",
}

const $secondaryButtonStyle: ViewStyle = {
  borderWidth: 0,
  borderRadius: 60,
  width: 80,
  height: 80,
  backgroundColor: colors.background,
  justifyContent: "center",
  alignItems: "center",
}
