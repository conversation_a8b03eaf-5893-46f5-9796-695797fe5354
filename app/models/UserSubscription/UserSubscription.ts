import { Instance, SnapshotIn, SnapshotOut, types } from "mobx-state-tree"
import { withSetPropAction } from "../helpers/withSetPropAction"
import { PricingTierModel, ProductModel } from "app/models/Merchant/Product"

export const UserSubscriptionModel = types
  .model("UserSubscription")
  .props({
    id: types.identifier,
    userId: types.string,
    productId: types.string,
    pricingTierId: types.maybeNull(types.string),
    product: types.maybeNull(types.reference(ProductModel)),
    pricingTier: types.maybeNull(types.reference(PricingTierModel)),
    startedOn: types.string,
    endedOn: types.maybeNull(types.string),
    billDate: types.maybeNull(types.string),
    createdAt: types.string,
    updatedAt: types.string,
  })
  .actions(withSetPropAction)
  .views((self) => ({
    get isActive() {
      return !self.endedOn || new Date(self.endedOn) > new Date()
    },
    get merchantName() {
      return self.product?.merchant?.name || "Unknown Merchant"
    },
    get merchantLogo() {
      return self.product?.merchant?.merchantGroup?.logo?.url || null
    },
    get merchantCategory() {
      const categories = self.product?.merchant?.merchantGroup?.categories
      if (categories && categories.length > 0) {
        return categories[0]
      }
      // Fallback to merchant group name or "Other"
      return self.product?.merchant?.merchantGroup?.name || "Other"
    },
    get productName() {
      return self.product?.name || "Unknown Product"
    },
    get cost() {
      return self.pricingTier?.cost || 0
    },
    get currency() {
      return self.pricingTier?.currency || "EUR"
    },
    get billingPeriod() {
      return self.pricingTier?.billingPeriod || "Monthly"
    },
    get hasTrialPeriod() {
      return self.pricingTier?.trialPeriodDays && self.pricingTier.trialPeriodDays > 0
    },
    get trialEndDate() {
      if (!self.hasTrialPeriod || !self.pricingTier?.trialPeriodDays) return null

      const startDate = new Date(self.startedOn)
      const endDate = new Date(startDate)
      endDate.setDate(startDate.getDate() + self.pricingTier.trialPeriodDays)

      return endDate
    },
    get isInTrialPeriod() {
      if (!self.hasTrialPeriod || !self.trialEndDate) return false

      const now = new Date()
      const startDate = new Date(self.startedOn)

      return now >= startDate && now <= self.trialEndDate && self.isActive
    },
    get trialDaysRemaining() {
      if (!self.isInTrialPeriod || !self.trialEndDate) return 0

      const now = new Date()
      const timeDiff = self.trialEndDate.getTime() - now.getTime()
      const daysRemaining = Math.ceil(timeDiff / (1000 * 3600 * 24))

      return Math.max(0, daysRemaining)
    },
    get trialPercentageRemaining() {
      if (!self.hasTrialPeriod || !self.pricingTier?.trialPeriodDays) return 0

      const totalDays = self.pricingTier.trialPeriodDays
      const remainingDays = self.trialDaysRemaining

      return Math.max(0, Math.min(100, (remainingDays / totalDays) * 100))
    },
    get nextBillDate() {
      if (!self.billDate) return null

      const initialBillDate = new Date(self.billDate)
      const today = new Date()
      const billingPeriod = self.pricingTier?.billingPeriod || "Monthly"

      // If the initial bill date is in the future, return it
      if (initialBillDate > today) {
        return self.billDate
      }

      // Calculate the next bill date based on billing period
      let nextDate = new Date(initialBillDate)

      while (nextDate <= today) {
        switch (billingPeriod) {
          case "Weekly":
            nextDate.setDate(nextDate.getDate() + 7)
            break
          case "Monthly":
            nextDate.setMonth(nextDate.getMonth() + 1)
            break
          case "Quarterly":
            nextDate.setMonth(nextDate.getMonth() + 3)
            break
          case "Annually":
            nextDate.setFullYear(nextDate.getFullYear() + 1)
            break
          default:
            // Default to monthly if unknown period
            nextDate.setMonth(nextDate.getMonth() + 1)
            break
        }
      }

      return nextDate.toISOString().split("T")[0] // Return as YYYY-MM-DD format
    },
  }))

export interface UserSubscription extends Instance<typeof UserSubscriptionModel> {}
export interface UserSubscriptionSnapshotOut extends SnapshotOut<typeof UserSubscriptionModel> {}
export interface UserSubscriptionSnapshotIn extends SnapshotIn<typeof UserSubscriptionModel> {}
export const createUserSubscriptionDefaultModel = () =>
  types.optional(UserSubscriptionModel, {
    id: '',
    userId: '',
    productId: '',
    pricingTierId: null,
    product: null,
    pricingTier: null,
    startedOn: '',
    endedOn: null,
    billDate: null,
    createdAt: '',
    updatedAt: '',
  })
