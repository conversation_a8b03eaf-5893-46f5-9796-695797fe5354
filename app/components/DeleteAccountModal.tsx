import React, { FC } from "react"
import { Modal, View, ViewStyle, TextStyle, Pressable } from "react-native"
import { Text, Button } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faX, faExclamationTriangle } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing } from "app/theme"

interface DeleteAccountModalProps {
  visible: boolean
  onCancel: () => void
  onConfirm: () => void
  isLoading?: boolean
}

export const DeleteAccountModal: FC<DeleteAccountModalProps> = ({
  visible,
  onCancel,
  onConfirm,
  isLoading = false,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}
    >
      <View style={$overlay}>
        <View style={$modal}>
          {/* Header */}
          <View style={$header}>
            <View style={$warningIconContainer}>
              <FontAwesomeIcon 
                icon={faExclamationTriangle} 
                color={colors.error} 
                size={24} 
              />
            </View>
            <Pressable style={$closeButton} onPress={onCancel} disabled={isLoading}>
              <FontAwesomeIcon icon={faX} color={colors.textDim} size={16} />
            </Pressable>
          </View>

          {/* Content */}
          <View style={$content}>
            <Text preset="heading" style={$title}>
              Delete My Account
            </Text>
            
            <Text style={$warningText}>
              This action will permanently delete your account and cannot be undone.
            </Text>

            <View style={$dataList}>
              <Text style={$dataListTitle}>The following data will be permanently deleted:</Text>
              <Text style={$dataItem}>• All your subscription records</Text>
              <Text style={$dataItem}>• Your profile information</Text>
              <Text style={$dataItem}>• Analytics and usage data</Text>
              <Text style={$dataItem}>• Notification preferences</Text>
              <Text style={$dataItem}>• All account settings</Text>
            </View>

            <Text style={$finalWarning}>
              Are you absolutely sure you want to delete your account?
            </Text>
          </View>

          {/* Actions */}
          <View style={$actions}>
            <Button
              text="Cancel"
              preset="default"
              style={$cancelButton}
              onPress={onCancel}
              disabled={isLoading}
            />
            <Button
              text={isLoading ? "Deleting..." : "Delete Account"}
              preset="filled"
              style={$deleteButton}
              textStyle={$deleteButtonText}
              onPress={onConfirm}
              disabled={isLoading}
            />
          </View>
        </View>
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.lg,
}

const $modal: ViewStyle = {
  backgroundColor: colors.background,
  borderRadius: 12,
  width: "100%",
  maxWidth: 400,
  maxHeight: "80%",
}

const $header: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  padding: spacing.lg,
  paddingBottom: spacing.md,
}

const $warningIconContainer: ViewStyle = {
  flex: 1,
  alignItems: "flex-start",
}

const $closeButton: ViewStyle = {
  padding: spacing.xs,
}

const $content: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingBottom: spacing.md,
}

const $title: TextStyle = {
  textAlign: "center",
  marginBottom: spacing.md,
  color: colors.error,
}

const $warningText: TextStyle = {
  fontSize: 16,
  color: colors.text,
  textAlign: "center",
  marginBottom: spacing.lg,
  lineHeight: 22,
}

const $dataList: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  padding: spacing.md,
  borderRadius: 8,
  marginBottom: spacing.lg,
}

const $dataListTitle: TextStyle = {
  fontSize: 14,
  fontWeight: "600",
  color: colors.text,
  marginBottom: spacing.sm,
}

const $dataItem: TextStyle = {
  fontSize: 14,
  color: colors.textDim,
  marginBottom: spacing.xs,
  lineHeight: 18,
}

const $finalWarning: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.error,
  textAlign: "center",
  lineHeight: 22,
}

const $actions: ViewStyle = {
  flexDirection: "row",
  padding: spacing.lg,
  paddingTop: spacing.md,
  gap: spacing.md,
}

const $cancelButton: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral200,
}

const $deleteButton: ViewStyle = {
  flex: 1,
  backgroundColor: colors.error,
}

const $deleteButtonText: TextStyle = {
  color: colors.palette.neutral100,
}
