# Delete My Account Feature

## Overview

The "Delete My Account" feature allows users to permanently delete their account and all associated data from the OneSub application. This feature implements a comprehensive data deletion process with proper security measures, user confirmation, and error handling.

## Features

### UI Components
- **Delete Account Button**: Red text link positioned below the sign out button in settings
- **Cross-Platform Modal**: Custom confirmation modal that works on both Android and iOS
- **Comprehensive Warning**: Clear messaging about data deletion and irreversibility
- **Loading States**: Visual feedback during the deletion process

### Security & Data Deletion
- **Secure Database Function**: Server-side function that ensures users can only delete their own accounts
- **Cascading Deletion**: Removes all user data from multiple tables:
  - `user_subscriptions` - All subscription records
  - `user_descriptors` - User profile descriptors
  - `pending_questions` - Queued profile questions
  - `notification_targets` - Push notification tokens
  - `user_profiles` - User profile information
  - `auth.users` - Authentication record (final step)

### Error Handling
- **Sentry Integration**: All errors are reported to Sentry for monitoring
- **Reactotron Logging**: Development logging for debugging
- **User-Friendly Messages**: Clear error messages for different failure scenarios
- **Network Error Handling**: Graceful handling of connectivity issues

## Implementation Details

### Components

#### DeleteAccountModal
```typescript
// Location: app/components/DeleteAccountModal.tsx
interface DeleteAccountModalProps {
  visible: boolean
  onCancel: () => void
  onConfirm: () => void
  isLoading?: boolean
}
```

Features:
- Cross-platform modal using React Native's Modal component
- Warning icon and comprehensive data deletion list
- Disabled state during deletion process
- Proper accessibility support

#### AccountDeletionService
```typescript
// Location: app/services/AccountDeletionService.ts
class AccountDeletionService {
  async deleteUserAccount(userId: string): Promise<ServiceResponse<void>>
  getErrorMessage(errorCode?: string): string
}
```

Features:
- Authentication verification before deletion
- Calls secure Supabase RPC function
- Automatic sign out after successful deletion
- Comprehensive error handling with specific error codes

### Database Functions

#### delete_user_account_secure
```sql
-- Secure function that verifies user identity
CREATE OR REPLACE FUNCTION delete_user_account_secure(user_id_to_delete UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
```

Security Features:
- Verifies requesting user matches account to be deleted
- Uses `auth.uid()` to ensure proper authentication
- Calls main deletion function with proper error handling

#### delete_user_account
```sql
-- Main deletion function with comprehensive data cleanup
CREATE OR REPLACE FUNCTION delete_user_account(user_id_to_delete UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
```

Features:
- Checks user existence before deletion
- Deletes data in proper order to avoid foreign key conflicts
- Handles optional tables (like notification_targets)
- Transaction safety with rollback on errors
- Detailed logging for each deletion step

### State Management

#### Auth Model Updates
```typescript
// Added to app/models/Auth/Auth.ts
deleteAccount() {
  // Clears all authentication state
}
```

#### AuthContext Integration
- Automatic state clearing when session ends
- Notification setup reset
- Store cleanup for user-specific data

## Usage Examples

### Basic Implementation
```typescript
import { accountDeletionService } from "app/services/AccountDeletionService"
import { DeleteAccountModal } from "app/components/DeleteAccountModal"

const [showDeleteModal, setShowDeleteModal] = useState(false)
const [isDeletingAccount, setIsDeletingAccount] = useState(false)

const handleDeleteAccount = async () => {
  setIsDeletingAccount(true)
  
  try {
    const result = await accountDeletionService.deleteUserAccount(userId)
    
    if (result.success) {
      // Account deleted successfully
      rootStore.auth.deleteAccount()
    } else {
      // Handle error
      const errorMessage = accountDeletionService.getErrorMessage(result.code)
      Alert.alert("Deletion Failed", errorMessage)
    }
  } catch (error) {
    Alert.alert("Error", "An unexpected error occurred.")
  } finally {
    setIsDeletingAccount(false)
  }
}
```

### Modal Usage
```typescript
<DeleteAccountModal
  visible={showDeleteModal}
  onCancel={() => setShowDeleteModal(false)}
  onConfirm={handleDeleteAccount}
  isLoading={isDeletingAccount}
/>
```

## Error Codes

| Code | Description | User Message |
|------|-------------|--------------|
| `UNAUTHORIZED` | User not authenticated or trying to delete another user's account | "You are not authorized to perform this action. Please sign in again." |
| `NETWORK_ERROR` | Network connectivity issues | "Network error occurred. Please check your connection and try again." |
| `SUPABASE_ERROR` | Database or server errors | "A server error occurred. Please try again later." |
| `UNKNOWN_ERROR` | Unexpected errors | "An unexpected error occurred. Please try again later." |

## Testing

### Manual Testing Checklist
- [ ] Delete account button appears in settings
- [ ] Modal opens with proper warning text
- [ ] Cancel button closes modal without deletion
- [ ] Delete button shows loading state
- [ ] Successful deletion clears all data and signs out user
- [ ] Error handling displays appropriate messages
- [ ] Works on both Android and iOS

### Database Verification
```sql
-- Verify user data is completely removed
SELECT COUNT(*) FROM user_subscriptions WHERE user_id = 'deleted-user-id';
SELECT COUNT(*) FROM user_profiles WHERE id = 'deleted-user-id';
SELECT COUNT(*) FROM auth.users WHERE id = 'deleted-user-id';
```

## Security Considerations

1. **User Verification**: Only authenticated users can delete accounts
2. **Self-Deletion Only**: Users can only delete their own accounts
3. **Transaction Safety**: All deletions happen in a single transaction
4. **Audit Trail**: Deletion events are logged for monitoring
5. **No Recovery**: Deleted data cannot be recovered (by design)

## Monitoring & Analytics

- All deletion attempts are logged to Sentry
- Success/failure rates can be monitored
- Error patterns help identify system issues
- User feedback on deletion reasons (future enhancement)

## Future Enhancements

1. **Deletion Reason Collection**: Optional feedback on why users delete accounts
2. **Data Export**: Allow users to export their data before deletion
3. **Soft Delete Option**: Temporary deactivation before permanent deletion
4. **Admin Override**: Support team ability to recover recently deleted accounts
5. **Batch Cleanup**: Automated cleanup of orphaned data
