import React from "react"
import { observer } from "mobx-react-lite"
import { ImageStyle, Pressable, TextStyle, View, ViewStyle } from "react-native"
import { AutoImage, Text } from "app/components"
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome"
import { faExclamationTriangle } from "@fortawesome/free-solid-svg-icons"
import { colors, spacing, typography } from "app/theme"
import { UserSubscription } from "app/models/UserSubscription/UserSubscription"

export interface SubscriptionItemProps {
  subscription: UserSubscription
  onPress?: (subscription: UserSubscription) => void
}

export const SubscriptionItem = observer(function SubscriptionItem({
  subscription,
  onPress
}: SubscriptionItemProps) {
  const handlePress = () => {
    onPress?.(subscription)
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-IE', {
      style: 'currency',
      currency: currency,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IE', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const renderTrialBadge = () => {
    if (!subscription.isInTrialPeriod) return null

    const daysRemaining = subscription.trialDaysRemaining
    const percentageRemaining = subscription.trialPercentageRemaining

    const isCritical = percentageRemaining <= 25
    const isWarning = percentageRemaining <= 50 && percentageRemaining > 25

    const timeText = daysRemaining === 1 ? "1 day left" : `${daysRemaining} days left`
    const showIcon = isWarning || isCritical

    const badgeStyle = [
      $trialBadge,
      isCritical && $trialBadgeCritical,
      isWarning && $trialBadgeWarning,
    ]

    const textStyle = [
      $trialBadgeText,
      isCritical && $trialBadgeTextCritical,
      isWarning && $trialBadgeTextWarning,
    ]

    const iconColor = isCritical ? colors.palette.neutral100 : colors.palette.accent500

    return (
      <View style={badgeStyle}>
        {showIcon && (
          <FontAwesomeIcon
            icon={faExclamationTriangle}
            color={iconColor}
            size={10}
            style={$trialBadgeIcon}
          />
        )}
        <Text style={textStyle}>
          {timeText}
        </Text>
      </View>
    )
  }

  return (
    <Pressable
      style={({ pressed }) => [
        $container,
        pressed && $containerPressed
      ]}
      onPress={handlePress}
    >
      <View style={$logoContainer}>
        {subscription.merchantLogo ? (
          <AutoImage
            source={{ uri: subscription.merchantLogo }}
            style={$logo}
            resizeMode="contain"
          />
        ) : (
          <View style={$logoPlaceholder}>
            <Text style={$logoPlaceholderText}>
              {subscription.merchantName.charAt(0).toUpperCase()}
            </Text>
          </View>
        )}
      </View>

      <View style={$contentContainer}>
        <View style={$headerRow}>
          <Text style={$merchantName} numberOfLines={1}>
            {subscription.merchantName}
          </Text>
          {subscription.cost > 0 && (
            <Text style={$cost}>
              {formatCurrency(subscription.cost, subscription.currency)}
            </Text>
          )}
        </View>

        <Text style={$productName} numberOfLines={1}>
          {subscription.productName}
        </Text>

        <View style={$metaRow}>
          <View style={$metaLeft}>
            <Text style={$metaText}>
              Started: {formatDate(subscription.startedOn)}
            </Text>
            {!subscription.isActive && subscription.endedOn && (
              <Text style={[$metaText, $endedText]}>
                Ended: {formatDate(subscription.endedOn)}
              </Text>
            )}
          </View>
          {renderTrialBadge()}
        </View>
      </View>

      <View style={$statusContainer}>
        <View style={[
          $statusIndicator,
          subscription.isActive ? $statusActive : $statusInactive
        ]} />
      </View>
    </Pressable>
  )
})

const $container: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: colors.palette.neutral100,
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  marginHorizontal: spacing.md,
  marginVertical: spacing.xxs,
  borderRadius: 12,
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 1 },
  shadowOpacity: 0.1,
  shadowRadius: 2,
  elevation: 2,
}

const $containerPressed: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  transform: [{ scale: 0.98 }],
}

const $logoContainer: ViewStyle = {
  width: 48,
  height: 48,
  marginRight: spacing.sm,
}

const $logo: ImageStyle = {
  width: 48,
  height: 48,
  borderRadius: 8,
}

const $logoPlaceholder: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 8,
  backgroundColor: colors.palette.primary500,
  alignItems: 'center',
  justifyContent: 'center',
}

const $logoPlaceholderText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 18,
  fontFamily: typography.primary.bold,
}

const $contentContainer: ViewStyle = {
  flex: 1,
  marginRight: spacing.sm,
}

const $headerRow: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: spacing.xxxs,
}

const $merchantName: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.semiBold,
  color: colors.text,
  flex: 1,
  marginRight: spacing.xs,
}

const $cost: TextStyle = {
  fontSize: 16,
  fontFamily: typography.primary.bold,
  color: colors.palette.primary500,
}

const $productName: TextStyle = {
  fontSize: 14,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
  marginBottom: spacing.xxxs,
}

const $metaRow: ViewStyle = {
  flexDirection: 'row',
  justifyContent: 'space-between',
  alignItems: 'center',
}

const $metaLeft: ViewStyle = {
  flex: 1,
}

const $metaText: TextStyle = {
  fontSize: 12,
  fontFamily: typography.primary.normal,
  color: colors.textDim,
}

const $endedText: TextStyle = {
  color: colors.error,
}

const $statusContainer: ViewStyle = {
  alignItems: 'center',
  justifyContent: 'center',
}

const $statusIndicator: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
}

const $statusActive: ViewStyle = {
  backgroundColor: colors.palette.accent500,
}

const $statusInactive: ViewStyle = {
  backgroundColor: colors.palette.neutral400,
}

// Trial badge styles
const $trialBadge: ViewStyle = {
  flexDirection: 'row',
  alignItems: 'center',
  paddingHorizontal: spacing.xs,
  paddingVertical: spacing.xxxs,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral300,
  borderWidth: 1,
  borderColor: colors.palette.neutral400,
}

const $trialBadgeWarning: ViewStyle = {
  backgroundColor: colors.palette.accent200,
  borderColor: colors.palette.accent400,
}

const $trialBadgeCritical: ViewStyle = {
  backgroundColor: colors.palette.angry500,
  borderColor: colors.palette.angry500,
}

const $trialBadgeText: TextStyle = {
  fontSize: 10,
  fontFamily: typography.primary.semiBold,
  color: colors.palette.neutral700,
  letterSpacing: 0.2,
}

const $trialBadgeTextWarning: TextStyle = {
  color: colors.palette.neutral800,
}

const $trialBadgeTextCritical: TextStyle = {
  color: colors.palette.neutral100,
}

const $trialBadgeIcon: ViewStyle = {
  marginRight: spacing.xxxs,
}
