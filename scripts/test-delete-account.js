/**
 * Test script for the delete account functionality
 * This script tests the database function without actually deleting a real user
 */

const { createClient } = require('@supabase/supabase-js')

// Use environment variables or replace with your actual values
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'your-supabase-url'
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-key'

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testDeleteAccountFunction() {
  console.log('🧪 Testing delete account function...')
  
  try {
    // Test 1: Check if the function exists
    console.log('1. Checking if delete_user_account_secure function exists...')
    const { data: functions, error: functionsError } = await supabase
      .rpc('pg_get_functiondef', { funcid: 'delete_user_account_secure'::regproc })
    
    if (functionsError) {
      console.log('❌ Function does not exist or cannot be accessed:', functionsError.message)
      return
    }
    
    console.log('✅ Function exists')
    
    // Test 2: Try to call the function with a non-existent user (should fail gracefully)
    console.log('2. Testing function with non-existent user...')
    const fakeUserId = '********-0000-0000-0000-************'
    
    const { error: callError } = await supabase
      .rpc('delete_user_account_secure', { user_id_to_delete: fakeUserId })
    
    if (callError) {
      console.log('✅ Function correctly rejected non-existent user:', callError.message)
    } else {
      console.log('⚠️ Function did not reject non-existent user (unexpected)')
    }
    
    // Test 3: Check permissions
    console.log('3. Checking function permissions...')
    const { data: permissions, error: permError } = await supabase
      .from('information_schema.routine_privileges')
      .select('*')
      .eq('routine_name', 'delete_user_account_secure')
      .eq('grantee', 'authenticated')
    
    if (permError) {
      console.log('❌ Could not check permissions:', permError.message)
    } else if (permissions && permissions.length > 0) {
      console.log('✅ Function has correct permissions for authenticated users')
    } else {
      console.log('❌ Function does not have permissions for authenticated users')
    }
    
    console.log('🎉 Delete account function tests completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testDeleteAccountFunction()
}

module.exports = { testDeleteAccountFunction }
