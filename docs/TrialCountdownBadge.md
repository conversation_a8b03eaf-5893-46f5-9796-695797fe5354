# Trial Countdown Integration

## Overview

Trial countdown functionality is integrated directly into individual `SubscriptionItem` components, displaying trial status contextually where it belongs - on each subscription that has an active trial period.

## Features

- **Contextual Display**: Trial badges appear directly on subscription items where they belong
- **Dynamic Visual States**: Three distinct visual states based on trial progress
- **Compact Design**: Small, unobtrusive badges that don't interfere with subscription details
- **Automatic Calculation**: Dynamically calculates trial percentage and days remaining
- **Smart Positioning**: Positioned in the meta row alongside subscription start date
- **Type Safety**: Full TypeScript support with proper type definitions

## Visual States

### 1. Default State (>50% of trial remaining)
- **Layout**: Small badge with "X days left" text
- **Background**: Light gray (`colors.palette.neutral300`)
- **Text**: Dark gray (`colors.palette.neutral700`)
- **Icon**: None

### 2. Warning State (≤50% but >25% remaining)
- **Layout**: Small badge with "X days left" + orange exclamation icon
- **Background**: Light amber (`colors.palette.accent200`)
- **Text**: Dark text (`colors.palette.neutral800`)
- **Icon**: Orange exclamation triangle

### 3. Critical State (≤25% remaining)
- **Layout**: Small badge with "X days left" + white exclamation icon
- **Background**: Red (`colors.palette.angry500`)
- **Text**: White (`colors.palette.neutral100`)
- **Icon**: White exclamation triangle

## Usage

### Basic Usage

```tsx
import { TrialCountdownBadge } from "app/components"

// In your component
<TrialCountdownBadge subscription={subscription} />
```

### With Custom Styling

```tsx
<TrialCountdownBadge
  subscription={subscription}
  style={{ marginTop: spacing.md }}
/>
```

### With Press Handler (Future Enhancement)

```tsx
<TrialCountdownBadge
  subscription={subscription}
  onPress={() => navigate("TrialDetails")}
/>
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `subscription` | `UserSubscription` | Yes | The subscription with trial period to display |
| `style` | `ViewStyle` | No | Optional style override for the badge container |
| `onPress` | `() => void` | No | Optional callback when badge is pressed |

## Integration with UserSubscriptionsScreen

The badge is integrated into the `UserSubscriptionsScreen` and positioned prominently at the top:

```tsx
{rootStore.primaryTrialSubscription && (
  <View style={$trialBadgeContainer}>
    <TrialCountdownBadge
      subscription={rootStore.primaryTrialSubscription}
    />
  </View>
)}
```

## Data Flow

### 1. Trial Detection
- The component checks `subscription.isInTrialPeriod` to determine if a trial is active
- Returns `null` if no active trial is found

### 2. Calculation Logic
- **Days Remaining**: `subscription.trialDaysRemaining`
- **Percentage Remaining**: `subscription.trialPercentageRemaining`
- **Trial End Date**: Calculated from `startedOn + trialPeriodDays`

### 3. State Determination
```tsx
const isCritical = percentageRemaining <= 25
const isWarning = percentageRemaining <= 50 && percentageRemaining > 25
const isDefault = percentageRemaining > 50
```

## Backend Integration

### Database Schema
The trial information comes from the `pricing_tiers` table:
- `trial_period_days`: Number of trial days for the pricing tier

### MobX Store Integration
- `rootStore.primaryTrialSubscription`: Returns the most urgent active trial
- `rootStore.activeTrialSubscriptions`: Returns all active trial subscriptions

## Error Handling

### Sentry Integration
```tsx
const badgeError = error instanceof Error ? error : new Error('TrialCountdownBadge render error')
reportSentryError(badgeError, ErrorType.HANDLED, ErrorExperience.UI)
```

### Graceful Degradation
- Component returns `null` on any error to prevent app crashes
- Errors are logged to Reactotron in development mode

## Development Logging

### Reactotron Logs
```tsx
if (__DEV__ && console.tron) {
  console.tron.log('🏷️ TrialCountdownBadge: Rendering badge', {
    subscriptionId: subscription.id,
    daysRemaining,
    percentageRemaining,
    merchantName: subscription.merchantName
  })
}
```

## Styling Architecture

### Base Styles
- Consistent padding and border radius
- Flexbox layout for icon and text alignment
- Self-contained sizing with `alignSelf: 'flex-start'`

### State-Specific Styles
- Modular style objects for each visual state
- Proper color contrast for accessibility
- Icon positioning and sizing

## Testing Strategy

### Unit Tests
- Test all three visual states
- Test edge cases (1 day remaining, no trial)
- Test error handling and graceful degradation

### Integration Tests
- Test with real subscription data
- Test MobX store integration
- Test screen positioning and layout

## Performance Considerations

- **Computed Values**: Uses MobX computed values for efficient re-rendering
- **Conditional Rendering**: Only renders when trial is active
- **Memoization**: Observer pattern prevents unnecessary re-renders

## Future Enhancements

1. **Interactive Features**: Add press handlers for trial management
2. **Animations**: Subtle animations for state transitions
3. **Customization**: More styling options and themes
4. **Notifications**: Integration with push notification system
5. **Multiple Trials**: Support for displaying multiple active trials

## Dependencies

- `react` and `react-native`: Core framework
- `mobx-react-lite`: State management and reactivity
- `@fortawesome/react-native-fontawesome`: Icons
- `app/components`: Internal component library
- `app/theme`: Design system
- `app/utils/crashReporting`: Error reporting
