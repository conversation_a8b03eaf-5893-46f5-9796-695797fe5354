import { LogoModel, MerchantGroupModel, MerchantModel } from "app/models/Merchant/MerchantGroup"
import { PricingTierModel, ProductModel } from "app/models/Merchant/Product"
import { UserSubscriptionModel } from "app/models/UserSubscription/UserSubscription"
import { RegionModel } from "app/models/Region/Region"
import { flow, Instance, SnapshotOut, types } from "mobx-state-tree"
import { PendingQuestionsStoreModel } from "app/models/Profile/PendingQuestions/PendingQuestions"
import { createAuthDefaultModel } from "app/models/Auth/Auth"
import { createSearchService } from "app/utils/inMemorySearch"
import { createUserStateDefaultModel } from "app/models/UserState/UserState"
import { createNotificationStoreDefaultModel } from "app/models/Notification/NotificationStore"
import { SubscriptionData, subscriptionService } from "app/services/SubscriptionService"
// @ts-ignore
export const RootStoreModel = types
  .model("RootStore")
  .props({
    auth: createAuthDefaultModel(),
    userState: createUserStateDefaultModel(),
    notifications: createNotificationStoreDefaultModel(),
    logos:  types.optional(types.array(LogoModel), []),
    merchantGroups: types.optional(types.array(MerchantGroupModel), []),
    merchants: types.optional(types.array(MerchantModel), []),
    products: types.optional(types.array(ProductModel), []),
    pricingTiers: types.optional(types.array(PricingTierModel), []),
    subscriptions: types.optional(types.array(UserSubscriptionModel), []),
    subscriptionsLastRefreshTime: types.maybeNull(types.number),
    subscriptionsLoading: types.optional(types.boolean, false),
    regions: types.optional(types.array(RegionModel), []),
    selectedRegion: types.maybeNull(types.safeReference(RegionModel)),
    pendingQuestions: types.optional(PendingQuestionsStoreModel, {}),
  })
  .views(self => ({
    get search() {
      return createSearchService(self)
    },
    get isSubscriptionsCacheValid() {
      if (!self.subscriptionsLastRefreshTime) return false
      const CACHE_DURATION = 1000 * 60 * 5 // 5 minutes in milliseconds
      return Date.now() - self.subscriptionsLastRefreshTime < CACHE_DURATION
    },
    get groupedSubscriptions() {
      const grouped = new Map<string, Instance<typeof UserSubscriptionModel>[]>()

      self.subscriptions.forEach(subscription => {
        const category = subscription.merchantCategory
        if (!grouped.has(category)) {
          grouped.set(category, [])
        }
        grouped.get(category)!.push(subscription)
      })

      return Array.from(grouped.entries()).map(([category, subscriptions]) => ({
        category,
        data: subscriptions.sort((a, b) => a.merchantName.localeCompare(b.merchantName))
      })).sort((a, b) => a.category.localeCompare(b.category))
    }
  }))
  .actions((self) => ({
    addProduct(product: Instance<typeof ProductModel>) {
      if (!self.products.includes(product)) {
        self.products.push(product);
      }
    },
    addMerchant(merchant: Instance<typeof MerchantModel>) {
      if (!self.merchants.includes(merchant)) {
        self.merchants.push(merchant);
      }
    },
    addMerchantGroup(merchantGroup: Instance<typeof MerchantGroupModel>) {
      if (!self.merchantGroups.includes(merchantGroup)) {
        self.merchantGroups.push(merchantGroup);
      }
    },
    addSubscription(subscription: Instance<typeof UserSubscriptionModel>) {
      if (!self.subscriptions.includes(subscription)) {
        self.subscriptions.push(subscription);
      }
    },
    addRegion(region: Instance<typeof RegionModel>) {
      if (!self.regions.includes(region)) {
        self.regions.push(region);
      }
    },
    setSelectedRegion(region: Instance<typeof RegionModel>) {
      self.selectedRegion = region;
    },
    clearNode(node: string){
      const nodeRef = self as any
      if (nodeRef[node] && typeof nodeRef[node].clear === 'function') {
        nodeRef[node].clear()
      }
    },

    fetchUserSubscriptions: flow(function* (userId: string, forceRefresh: boolean = false) {
      if (!forceRefresh && self.isSubscriptionsCacheValid) {
        if (__DEV__ && console.tron) {
          console.tron.log('📋 Using cached subscriptions')
        }
        return
      }

      self.subscriptionsLoading = true

      try {
        const response = yield subscriptionService.getUserSubscriptions(userId)

        if (response.success && response.data) {
          self.subscriptions.clear()
          self.pricingTiers.clear()
          self.products.clear()
          self.merchants.clear()
          self.merchantGroups.clear()

          response.data.forEach((subscriptionData: SubscriptionData) => {
            const merchant = subscriptionData.products.merchants
            const merchantGroup = merchant?.merchant_groups
            const product = subscriptionData.products

            if (merchantGroup && !self.merchantGroups.find(mg => mg.id === merchantGroup.id)) {
              self.merchantGroups.push({
                id: merchantGroup.id,
                name: merchantGroup.name,
                onlineOnly: merchantGroup.online_only,
                description: merchantGroup.description,
                categories: merchantGroup.categories || [],
                logo: merchantGroup.logo ? {
                  url: merchantGroup.logo.url,
                  width: merchantGroup.logo.width,
                  height: merchantGroup.logo.height
                } : null,
                merchants: [],
                updatedAt: new Date().toISOString()
              })
            }

            if (merchant && !self.merchants.find(m => m.id === merchant.id)) {
              self.merchants.push({
                id: merchant.id,
                name: merchant.name,
                categories: merchantGroup?.categories || [],
                location: null,
                merchantGroup: merchantGroup?.id || null,
                region: null,
                subscriptionProductIds: [product.id],
                updatedAt: new Date().toISOString()
              })
            }

            // Add pricing tier to separate collection if it exists
            if (subscriptionData.pricing_tiers && !self.pricingTiers.find(pt => pt.id === subscriptionData?.pricing_tiers?.id)) {
              self.pricingTiers.push({
                id: subscriptionData.pricing_tiers.id,
                name: subscriptionData.pricing_tiers.name,
                cost: subscriptionData.pricing_tiers.cost,
                currency: subscriptionData.pricing_tiers.currency,
                description: subscriptionData.pricing_tiers.description,
                billingPeriod: subscriptionData.pricing_tiers.billing_period || "Monthly",
                trialPeriodDays: subscriptionData.pricing_tiers.trial_period_days,
                idealFor: [],
                updatedAt: new Date().toISOString()
              })
            }

            if (!self.products.find(p => p.id === product.id)) {
              self.products.push({
                id: product.id,
                merchant: merchant?.id || '',
                name: product.name,
                description: product.description,
                idealFor: [],
                pricingTiers: subscriptionData.pricing_tiers ? [subscriptionData.pricing_tiers.id] : [],
                updatedAt: new Date().toISOString()
              })
            }

            self.subscriptions.push({
              id: subscriptionData.id,
              userId: subscriptionData.user_id,
              productId: subscriptionData.product_id,
              pricingTierId: subscriptionData.pricing_tier_id,
              product: product.id,
              pricingTier: subscriptionData.pricing_tiers?.id || null,
              startedOn: subscriptionData.started_on,
              endedOn: subscriptionData.ended_on,
              billDate: subscriptionData.bill_date,
              createdAt: subscriptionData.created_at,
              updatedAt: subscriptionData.updated_at
            })
          })

          self.subscriptionsLastRefreshTime = Date.now()
        }
      } catch (error) {
        if (__DEV__ && console.tron) {
          console.tron.error('❌ Failed to fetch subscriptions in store:', error)
        }
      } finally {
        self.subscriptionsLoading = false
      }
    }),

    bustSubscriptionsCache() {
      self.subscriptionsLastRefreshTime = null
    },

    addSubscriptionToStore: flow(function* (subscriptionData: SubscriptionData) {
      try {
        const merchant = subscriptionData.products.merchants
        const merchantGroup = merchant?.merchant_groups
        const product = subscriptionData.products

        // Add merchant group if not exists
        if (merchantGroup && !self.merchantGroups.find(mg => mg.id === merchantGroup.id)) {
          self.merchantGroups.push({
            id: merchantGroup.id,
            name: merchantGroup.name,
            onlineOnly: merchantGroup.online_only,
            description: merchantGroup.description,
            categories: merchantGroup.categories || [],
            logo: merchantGroup.logo ? {
              url: merchantGroup.logo.url,
              width: merchantGroup.logo.width,
              height: merchantGroup.logo.height
            } : null,
            merchants: [],
            updatedAt: new Date().toISOString()
          })
        }

        // Add merchant if not exists
        if (merchant && !self.merchants.find(m => m.id === merchant.id)) {
          self.merchants.push({
            id: merchant.id,
            name: merchant.name,
            categories: merchantGroup?.categories || [],
            location: null,
            merchantGroup: merchantGroup?.id || null,
            region: null,
            subscriptionProductIds: [product.id],
            updatedAt: new Date().toISOString()
          })
        }

        // Add pricing tier to separate collection if it exists
        if (subscriptionData.pricing_tiers && !self.pricingTiers.find(pt => pt.id === subscriptionData?.pricing_tiers?.id)) {
          self.pricingTiers.push({
            id: subscriptionData.pricing_tiers.id,
            name: subscriptionData.pricing_tiers.name,
            cost: subscriptionData.pricing_tiers.cost,
            currency: subscriptionData.pricing_tiers.currency,
            description: subscriptionData.pricing_tiers.description,
            billingPeriod: subscriptionData.pricing_tiers.billing_period || "Monthly",
            trialPeriodDays: subscriptionData.pricing_tiers.trial_period_days,
            idealFor: [],
            updatedAt: new Date().toISOString()
          })
        }

        // Add product if not exists
        if (!self.products.find(p => p.id === product.id)) {
          self.products.push({
            id: product.id,
            merchant: merchant?.id || '',
            name: product.name,
            description: product.description,
            idealFor: [],
            pricingTiers: subscriptionData.pricing_tiers ? [subscriptionData.pricing_tiers.id] : [],
            updatedAt: new Date().toISOString()
          })
        }

        // Add the new subscription
        self.subscriptions.push({
          id: subscriptionData.id,
          userId: subscriptionData.user_id,
          productId: subscriptionData.product_id,
          pricingTierId: subscriptionData.pricing_tier_id,
          product: product.id,
          pricingTier: subscriptionData.pricing_tiers?.id || null,
          startedOn: subscriptionData.started_on,
          endedOn: subscriptionData.ended_on,
          billDate: subscriptionData.bill_date,
          createdAt: subscriptionData.created_at,
          updatedAt: subscriptionData.updated_at
        })

        if (__DEV__ && console.tron) {
          console.tron.log('✅ Added new subscription to store:', subscriptionData.id)
        }
      } catch (error) {
        if (__DEV__ && console.tron) {
          console.tron.error('❌ Failed to add subscription to store:', error)
        }
      }
    })
  }))

// @ts-ignore
export interface RootStore extends Instance<typeof RootStoreModel> {
  // Properties
  subscriptions: Instance<typeof UserSubscriptionModel>[]
  subscriptionsLoading: boolean
  subscriptionsLastRefreshTime: number | null
  pricingTiers: Instance<typeof PricingTierModel>[]
  products: Instance<typeof ProductModel>[]
  merchants: Instance<typeof MerchantModel>[]
  merchantGroups: Instance<typeof MerchantGroupModel>[]

  // Views
  isSubscriptionsCacheValid: boolean
  groupedSubscriptions: Array<{
    category: string
    data: Instance<typeof UserSubscriptionModel>[]
  }>

  // Actions
  fetchUserSubscriptions: (userId: string, forceRefresh?: boolean) => Promise<void>
}


/**
 * The data of a RootStore.
 */
export interface RootStoreSnapshot extends SnapshotOut<typeof RootStoreModel> {}

